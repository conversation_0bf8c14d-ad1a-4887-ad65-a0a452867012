:root {
    /* COLORS */
    --color-dark-0: #0D0E16;
    --color-dark-1: #1B1C2A;
    --color-dark-2: #282B3A;
    --color-dark-3: #666F80;
    --color-dark-accent:  #00C4B4;
    --color-dark-contrast:  #FFC75F;

    --color-light-0: #FFFFFF;
    --color-light-1: #FFFFFF;
    --color-light-2: #E3F2F8;
    --color-light-3: #afb1b5;
    --color-light-accent: blue;
    --color-light-contrast:  #E3474B;

    /* DARK THEME */
    --color-background: var(--color-dark-0);
    --color-panel: var(--color-dark-1);
    --color-panel-selected: var(--color-dark-2);
    --color-panel-text: var(--color-dark-3);
    --color-text: var(--color-light-0);
    --color-accent: var(--color-dark-accent);
    --color-contrast: var(--color-dark-contrast);
}

[data-theme="light"] {
    /* LIGHT THEME */
    --color-background: var(--color-light-0);
    --color-panel: var(--color-light-1);
    --color-panel-selected: var(--color-light-2);
    --color-panel-text: var(--color-light-3);
    --color-text: var(--color-dark-0);
    --color-accent: var(--color-light-accent);
    --color-contrast: var(--color-light-contrast);
}
:root {
    font-size: 16px;
}
.text-shine {
    background: linear-gradient(
        -45deg,
        var(--color-dark-accent) 0%,
        var(--color-light-accent) 25%,
        var(--color-light-contrast) 50%,
        var(--color-dark-contrast) 75%,
        var(--color-dark-accent) 100%
    );
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine 4s linear infinite;
}

@keyframes shine {
    100% {
        background-position: -200% center;
    }
}
:root {
    --button-size: 48px;
}

*, :after, :before {
    box-sizing: border-box;
}

*::-webkit-scrollbar {
    width: 0;
}

*::-webkit-scrollbar-track {
    border-left: 0 solid transparent;
}

*::-webkit-scrollbar-thumb {
    border-radius: 10px;
}

.panel[data-v-787ce242] {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    gap: 5px;
    background-color: var(--color-panel);
}
.line[data-v-787ce242] {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 5px;
}
.line-language[data-v-787ce242] {
    gap: 4px;
    font-size: 0.9rem;
    font-weight: 400;
    margin-bottom: 3px;
}
.line-language-title[data-v-787ce242] {
    width: 90px;
    font-weight: 500;
}
.buttons[data-v-787ce242] {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
}
.input[data-v-787ce242] {
    width: 100%;
    height: 32px;
    padding: 0 10px;
    border: 1px solid var(--color-panel-text);
    background-color: var(--color-light-0);
    color: var(--color-dark-0);
    border-radius: 6px;
    font-size: 0.8rem;
    font-family: inherit;
    cursor: pointer;
}
.language[data-v-787ce242] {
    appearance: none;
    /* Стрелка */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='20' height='20' fill='%234498db'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 0;
}
.action[data-v-787ce242] {
    position: relative;
    width: var(--button-size);
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px;
    border: 0;
    padding: 4px;
    color: var(--color-panel-text);
    font-size: 1rem;
    font-weight: 700;
    font-family: inherit;
    border-radius: 8px;
    background-color: transparent;
    cursor: pointer;
}
.action-line-length svg[data-v-787ce242] {
    fill: var(--color-panel-text);
}
.action-icon[data-v-787ce242] {
    width: 24px;
    height: 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.action-title[data-v-787ce242] {
    height: 20px;
    font-size: 9px;
    color: var(--color-panel-text);
    user-select: none;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-content: center;
    align-items: center;
    text-align: center;
}
.action svg[data-v-787ce242] {
    fill: var(--color-panel-text);
}
.action[data-v-787ce242]:hover {
    background-color: var(--color-panel-selected);
}
.action[data-v-787ce242]:active {
    background-color: var(--color-panel-selected);
}
.line-block-settings .action:active svg[data-v-787ce242],
.line-block-settings .action-active svg[data-v-787ce242],
.line-block-settings .action[data-v-787ce242]:active,
.line-block-settings .action-active[data-v-787ce242] {
    fill: var(--color-accent);
    color: var(--color-accent);
}
.line-download .action:active svg[data-v-787ce242],
.line-download .action-active svg[data-v-787ce242],
.line-download .action[data-v-787ce242]:active,
.line-download .action-active[data-v-787ce242] {
    fill: var(--color-contrast);
    color: var(--color-contrast);
}
.action[data-v-787ce242]:disabled {
    opacity: 0.4;
    background-color: transparent;
    color: var(--color-panel-text);
}
.action:disabled svg[data-v-787ce242] {
    fill: var(--color-panel-text);
}
@keyframes tip-show-787ce242 {
0% {
        opacity: 0;
}
100% {
        opacity: 1;
}
}

.subtitles[data-v-7516df5a] {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 6px;
    overflow-y: auto;
}
.subtitle[data-v-7516df5a] {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 5px;
    padding: 10px 12px;
    border: 1px solid transparent;
    border-radius: 10px;
    background-color: var(--color-panel);
    font-size: 1rem;
    opacity: 0;
    transform: translateY(10px);
    animation: fadeIn-7516df5a 0.5s ease-in-out forwards;
    animation-delay: 0.05s;
}
.subtitle[data-v-7516df5a]:hover {
    border: 1px solid var(--color-accent);
    background: var(--color-panel-selected);
}
.subtitle-header[data-v-7516df5a] {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    height: auto;
}
.time[data-v-7516df5a] {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    color: var(--color-accent);
    margin-right: 4px;
    font-weight: 700;
    cursor: pointer;
    height: 26px;
}
.subtitle:hover .subtitle-buttons[data-v-7516df5a] {
    display: flex;
}
.subtitle-buttons[data-v-7516df5a] {
    display: none;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 5px;
    height: 26px;
}
.subtitle-buttons-reverse[data-v-7516df5a] {
    flex-direction: row-reverse !important;
}
.subtitle-buttons button[data-v-7516df5a] {
    height: auto;
    font-size: 12px;
    font-weight: 600;
    padding: 0 6px;
    color: var(--color-panel-text);
    background: transparent;
    border: 0;
    border-radius: 7px;
    cursor: pointer;
}
.subtitle-buttons button[data-v-7516df5a]:hover {
    background: var(--color-panel-selected);
}
.subtitle-buttons button svg[data-v-7516df5a] {
    fill: var(--color-panel-text);
}
.subtitle-buttons button[data-v-7516df5a]:active,
.subtitle-buttons button:active svg[data-v-7516df5a],
.subtitle-buttons button.active[data-v-7516df5a] {
    color: var(--color-accent);
    fill: var(--color-accent);
}
.text[data-v-7516df5a] {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 10px;
}
.text-reverse[data-v-7516df5a] {
    flex-direction: column-reverse !important;
}
.highlighted-text[data-v-7516df5a] {
    color: var(--color-contrast);
}
.highlighted-translate[data-v-7516df5a] {
    color: var(--color-contrast);
}
.subtitles-blocks-item span[data-v-7516df5a] {
    line-height: 1.3rem;
}
@keyframes fadeIn-7516df5a {
from {
        opacity: 0;
        transform: translateY(10px);
}
to {
        opacity: 1;
        transform: translateY(0);
}
}
@keyframes slideUp-7516df5a {
from {
        opacity: 0;
        transform: translateY(20px);
}
to {
        opacity: 1;
        transform: translateY(0);
}
}
.error-message[data-v-7516df5a] {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 12px;
    border: 1px solid transparent;
    border-radius: 10px;
    background-color: var(--color-contrast);
    color: var(--color-dark-0);
    font-size: 1rem;
    opacity: 0;
    transform: translateY(10px);
    animation: fadeIn-7516df5a 0.5s ease-in-out forwards;
}

.ysd-rating[data-v-c3f5eabb] {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 7px;
    background: rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(5.2px);
    font-size: 16px;
    font-weight: 700;
    z-index: 7;
}
.ysd-rating-form[data-v-c3f5eabb] {
    max-width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    gap: 12px;
    background-color: white;
    border-radius: 10px;
    font-family: "Roboto", sans-serif;
    font-weight: 300;
    line-height: normal;
    text-shadow: 0 0 0;
    box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.1);
}
.ysd-rating-title[data-v-c3f5eabb] {
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    color: #212121;
}
.ysd-rating-stars[data-v-c3f5eabb] {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    align-items: center;
    gap: 4px;
}
.ysd-rating-stars svg[data-v-c3f5eabb] {
    fill: #D3D3D3;
}
.ysd-rating-stars a[data-v-c3f5eabb] {
    cursor: pointer;
}
.ysd-rating-stars a:hover svg[data-v-c3f5eabb],
.ysd-rating-stars a:hover ~ a svg[data-v-c3f5eabb] {
    fill: #FFD12F;
}
.thumbs[data-v-c3f5eabb] {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}
.thumbs svg[data-v-c3f5eabb] {
    width: 24px;
    height: 24px;
}
.thumb-down svg[data-v-c3f5eabb] {
    position: relative;
    bottom: 1px;
}
.thumb-up:hover svg[data-v-c3f5eabb] {
    fill: #212121;
}
.thumb-down svg[data-v-c3f5eabb] {
    position: relative;
    bottom: -3px;
}
.thumb-down:hover svg[data-v-c3f5eabb] {
    fill: #212121;
}
.ysd-write-review[data-v-c3f5eabb] {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 3px;
    cursor: pointer;
}
.ysd-write-review-accept[data-v-c3f5eabb] {
    padding: 7px 25px;
    border-radius: 50px;
    border: 1px solid #747775;
    color: #0B57D0;
    font-size: 14px;
    font-weight: 600;
}
.ysd-write-review-accept[data-v-c3f5eabb]:hover,
.ysd-write-review-accept[data-v-c3f5eabb]:active {
    border: 1px solid #0B57D0;
    background-color: #0B57D0;
    color: white;
}
.ysd-write-review-reject[data-v-c3f5eabb] {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #828282;
    padding: 4px 0;
}

#app {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-family: "Ubuntu", "Roboto", sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--color-background);
}
.app {
    width: 100vw;
    max-width: 600px;
    height: 100vh;
    padding: 6px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 6px;
    border-radius: 10px;
    background-color: var(--color-background);
    color: var(--color-text);
}
.header {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1;
    border-radius: 10px;
    overflow: clip;
}
.main {
    width: 100%;
    min-height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    border-radius: 10px;
    z-index: 1;
}
.loader {
    width: 100%;
    height: 1px;
    background: var(--color-background);
    order: 0;
    align-self: stretch;
    flex-grow: 0;
}
.loader-active {
    animation: shine 1s linear infinite;
    background: linear-gradient(
        90deg,
        var(--color-accent) 0%,
        var(--color-background) 25%,
        var(--color-contrast) 50%,
        var(--color-background) 75%,
        var(--color-accent) 100%
    );
    background-size: 200% 200%;
}
.noise {
    position: absolute;
    width: 100%;
    min-height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    opacity: 13%;
    background-repeat: repeat;
    background-size: 60px;
    pointer-events: none;
    background-image: url('/noise.png');
    z-index: 9;
}
.app-loader {
    position: absolute;
    width: 100%;
    min-height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    background-color: var(--color-panel);
    font-size: 16px;
    font-weight: 700;
    z-index: 8;
}
