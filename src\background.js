(function () {
    "use strict";
    const byteToHex = [];
    for (let i = 0; i < 256; ++i) {
        byteToHex.push((i + 256).toString(16).slice(1));
    }
    function unsafeStringify(arr, offset = 0) {
        return (
            byteToHex[arr[offset + 0]] +
            byteToHex[arr[offset + 1]] +
            byteToHex[arr[offset + 2]] +
            byteToHex[arr[offset + 3]] +
            "-" +
            byteToHex[arr[offset + 4]] +
            byteToHex[arr[offset + 5]] +
            "-" +
            byteToHex[arr[offset + 6]] +
            byteToHex[arr[offset + 7]] +
            "-" +
            byteToHex[arr[offset + 8]] +
            byteToHex[arr[offset + 9]] +
            "-" +
            byteToHex[arr[offset + 10]] +
            byteToHex[arr[offset + 11]] +
            byteToHex[arr[offset + 12]] +
            byteToHex[arr[offset + 13]] +
            byteToHex[arr[offset + 14]] +
            byteToHex[arr[offset + 15]]
        ).toLowerCase();
    }
    let getRandomValues;
    const rnds8 = new Uint8Array(16);
    function rng() {
        if (!getRandomValues) {
            if (typeof crypto === "undefined" || !crypto.getRandomValues) {
                throw new Error(
                    "crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported",
                );
            }
            getRandomValues = crypto.getRandomValues.bind(crypto);
        }
        return getRandomValues(rnds8);
    }
    const randomUUID =
        typeof crypto !== "undefined" &&
        crypto.randomUUID &&
        crypto.randomUUID.bind(crypto);
    const native = { randomUUID };
    function v4(options, buf, offset) {
        var _a;
        if (native.randomUUID && true && !options) {
            return native.randomUUID();
        }
        options = options || {};
        const rnds =
            options.random ??
            ((_a = options.rng) == null ? void 0 : _a.call(options)) ??
            rng();
        if (rnds.length < 16) {
            throw new Error("Random bytes length must be >= 16");
        }
        rnds[6] = (rnds[6] & 15) | 64;
        rnds[8] = (rnds[8] & 63) | 128;
        return unsafeStringify(rnds);
    }
    chrome.runtime.onInstalled.addListener(async (details) => {
        if (details.reason === chrome.runtime.OnInstalledReason.INSTALL) {
            let userUuid;
            const storageData = await chrome.storage.local.get(null);
            if (storageData.userUuid) {
                userUuid = storageData.userUuid;
            } else {
                userUuid = v4();
                chrome.storage.local.set({ userUuid });
            }
            const manifest = chrome.runtime.getManifest();
            const welcomeUrl = new URL(
                "https://antonkhoteev.com/khoteev-api/ysd/welcome",
            );
            if (userUuid) welcomeUrl.searchParams.append("userUuid", userUuid);
            if (manifest.version)
                welcomeUrl.searchParams.append("version", manifest.version);
            const uninstallUrl = new URL(
                "https://antonkhoteev.com/khoteev-api/ysd/uninstall",
            );
            if (userUuid)
                uninstallUrl.searchParams.append("userUuid", userUuid);
            chrome.tabs.create({ url: welcomeUrl.toString() });
            chrome.runtime.setUninstallURL(uninstallUrl.toString());
        }
    });
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        var _a;
        if (message.type === "CONTENT_ENABLE_SIDEBAR") {
            (async () => {
                await chrome.sidePanel.setOptions({
                    tabId: sender.tab.id,
                    path: "src/side-panel.html",
                    enabled: true,
                });
            })();
        }
        if (message.type === "CONTENT_OPEN_SIDEBAR") {
            (async () => {
                chrome.sidePanel.open({ tabId: sender.tab.id });
            })();
        }
        if (message.type === "CONTENT_CLOSE_SIDEBAR") {
            (async () => {
                await chrome.sidePanel.setOptions({
                    tabId: sender.tab.id,
                    path: "src/side-panel.html",
                    enabled: false,
                });
            })();
        }
        if (message.type === "SIDEBAR_CLOSE_SIDEBAR") {
            (() => {
                chrome.tabs.query(
                    { active: true, currentWindow: true },
                    (tabs) => {
                        if (tabs.length > 0) {
                            chrome.sidePanel.setOptions({
                                tabId: tabs[0].id,
                                path: "src/side-panel.html",
                                enabled: false,
                            });
                        }
                    },
                );
            })();
        }
        if (message.type === "CONTENT_GET_TAB_ID") {
            sendResponse({
                tabId:
                    (_a = sender == null ? void 0 : sender.tab) == null
                        ? void 0
                        : _a.id,
            });
            return true;
        }
        if (message.type === "SIDEBAR_GET_TAB_ID") {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs.length > 0) {
                    sendResponse({ tabId: tabs[0].id });
                }
            });
            return true;
        }
        if (message.type === "MESSAGE_TO_TAB_PROXY") {
            chrome.tabs.sendMessage(message.tabId, {
                type: "MESSAGE_TO_TAB",
                value: message.value,
            });
        }
    });
    chrome.tabs.onUpdated.addListener(async (tabId, info, tab) => {
        if (!tab.url) {
            return;
        }
        const url = new URL(tab.url);
        if (url.origin === "https://www.youtube.com") {
            await chrome.sidePanel.setOptions({
                tabId,
                path: "src/side-panel.html",
                enabled: true,
            });
        } else {
            await chrome.sidePanel.setOptions({
                tabId,
                enabled: false,
            });
        }
    });
})();
