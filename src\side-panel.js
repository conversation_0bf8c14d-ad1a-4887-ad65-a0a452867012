(function polyfill() {
    const relList = document.createElement("link").relList;
    if (relList && relList.supports && relList.supports("modulepreload")) {
        return;
    }
    for (const link of document.querySelectorAll('link[rel="modulepreload"]')) {
        processPreload(link);
    }
    new MutationObserver((mutations) => {
        for (const mutation of mutations) {
            if (mutation.type !== "childList") {
                continue;
            }
            for (const node of mutation.addedNodes) {
                if (node.tagName === "LINK" && node.rel === "modulepreload")
                    processPreload(node);
            }
        }
    }).observe(document, { childList: true, subtree: true });
    function getFetchOpts(link) {
        const fetchOpts = {};
        if (link.integrity) fetchOpts.integrity = link.integrity;
        if (link.referrerPolicy) fetchOpts.referrerPolicy = link.referrerPolicy;
        if (link.crossOrigin === "use-credentials")
            fetchOpts.credentials = "include";
        else if (link.crossOrigin === "anonymous")
            fetchOpts.credentials = "omit";
        else fetchOpts.credentials = "same-origin";
        return fetchOpts;
    }
    function processPreload(link) {
        if (link.ep) return;
        link.ep = true;
        const fetchOpts = getFetchOpts(link);
        fetch(link.href, fetchOpts);
    }
})();
/**
 * @vue/shared v3.5.13
 * (c) 2018-present Yuxi (Evan) You and Vue contributors
 * @license MIT
 **/
/*! #__NO_SIDE_EFFECTS__ */
// @__NO_SIDE_EFFECTS__
function makeMap(str) {
    const map = /* @__PURE__ */ Object.create(null);
    for (const key of str.split(",")) map[key] = 1;
    return (val) => val in map;
}
const EMPTY_OBJ = {};
const EMPTY_ARR = [];
const NOOP = () => {};
const NO = () => false;
const isOn = (key) =>
    key.charCodeAt(0) === 111 &&
    key.charCodeAt(1) === 110 && // uppercase letter
    (key.charCodeAt(2) > 122 || key.charCodeAt(2) < 97);
const isModelListener = (key) => key.startsWith("onUpdate:");
const extend = Object.assign;
const remove = (arr, el) => {
    const i = arr.indexOf(el);
    if (i > -1) {
        arr.splice(i, 1);
    }
};
const hasOwnProperty$1 = Object.prototype.hasOwnProperty;
const hasOwn = (val, key) => hasOwnProperty$1.call(val, key);
const isArray = Array.isArray;
const isMap = (val) => toTypeString(val) === "[object Map]";
const isSet = (val) => toTypeString(val) === "[object Set]";
const isDate = (val) => toTypeString(val) === "[object Date]";
const isFunction = (val) => typeof val === "function";
const isString = (val) => typeof val === "string";
const isSymbol = (val) => typeof val === "symbol";
const isObject = (val) => val !== null && typeof val === "object";
const isPromise$1 = (val) => {
    return (
        (isObject(val) || isFunction(val)) &&
        isFunction(val.then) &&
        isFunction(val.catch)
    );
};
const objectToString = Object.prototype.toString;
const toTypeString = (value) => objectToString.call(value);
const toRawType = (value) => {
    return toTypeString(value).slice(8, -1);
};
const isPlainObject$1 = (val) => toTypeString(val) === "[object Object]";
const isIntegerKey = (key) =>
    isString(key) &&
    key !== "NaN" &&
    key[0] !== "-" &&
    "" + parseInt(key, 10) === key;
const isReservedProp = /* @__PURE__ */ makeMap(
    // the leading comma is intentional so empty string "" is also included
    ",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted",
);
const cacheStringFunction = (fn) => {
    const cache = /* @__PURE__ */ Object.create(null);
    return (str) => {
        const hit = cache[str];
        return hit || (cache[str] = fn(str));
    };
};
const camelizeRE = /-(\w)/g;
const camelize = cacheStringFunction((str) => {
    return str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ""));
});
const hyphenateRE = /\B([A-Z])/g;
const hyphenate = cacheStringFunction((str) =>
    str.replace(hyphenateRE, "-$1").toLowerCase(),
);
const capitalize = cacheStringFunction((str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
});
const toHandlerKey = cacheStringFunction((str) => {
    const s = str ? `on${capitalize(str)}` : ``;
    return s;
});
const hasChanged = (value, oldValue) => !Object.is(value, oldValue);
const invokeArrayFns = (fns, ...arg) => {
    for (let i = 0; i < fns.length; i++) {
        fns[i](...arg);
    }
};
const def = (obj, key, value, writable = false) => {
    Object.defineProperty(obj, key, {
        configurable: true,
        enumerable: false,
        writable,
        value,
    });
};
const looseToNumber = (val) => {
    const n = parseFloat(val);
    return isNaN(n) ? val : n;
};
let _globalThis;
const getGlobalThis = () => {
    return (
        _globalThis ||
        (_globalThis =
            typeof globalThis !== "undefined"
                ? globalThis
                : typeof self !== "undefined"
                  ? self
                  : typeof window !== "undefined"
                    ? window
                    : typeof global !== "undefined"
                      ? global
                      : {})
    );
};
function normalizeStyle(value) {
    if (isArray(value)) {
        const res = {};
        for (let i = 0; i < value.length; i++) {
            const item = value[i];
            const normalized = isString(item)
                ? parseStringStyle(item)
                : normalizeStyle(item);
            if (normalized) {
                for (const key in normalized) {
                    res[key] = normalized[key];
                }
            }
        }
        return res;
    } else if (isString(value) || isObject(value)) {
        return value;
    }
}
const listDelimiterRE = /;(?![^(]*\))/g;
const propertyDelimiterRE = /:([^]+)/;
const styleCommentRE = /\/\*[^]*?\*\//g;
function parseStringStyle(cssText) {
    const ret = {};
    cssText
        .replace(styleCommentRE, "")
        .split(listDelimiterRE)
        .forEach((item) => {
            if (item) {
                const tmp = item.split(propertyDelimiterRE);
                tmp.length > 1 && (ret[tmp[0].trim()] = tmp[1].trim());
            }
        });
    return ret;
}
function normalizeClass(value) {
    let res = "";
    if (isString(value)) {
        res = value;
    } else if (isArray(value)) {
        for (let i = 0; i < value.length; i++) {
            const normalized = normalizeClass(value[i]);
            if (normalized) {
                res += normalized + " ";
            }
        }
    } else if (isObject(value)) {
        for (const name in value) {
            if (value[name]) {
                res += name + " ";
            }
        }
    }
    return res.trim();
}
const specialBooleanAttrs = `itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly`;
const isSpecialBooleanAttr = /* @__PURE__ */ makeMap(specialBooleanAttrs);
function includeBooleanAttr(value) {
    return !!value || value === "";
}
function looseCompareArrays(a, b) {
    if (a.length !== b.length) return false;
    let equal = true;
    for (let i = 0; equal && i < a.length; i++) {
        equal = looseEqual(a[i], b[i]);
    }
    return equal;
}
function looseEqual(a, b) {
    if (a === b) return true;
    let aValidType = isDate(a);
    let bValidType = isDate(b);
    if (aValidType || bValidType) {
        return aValidType && bValidType ? a.getTime() === b.getTime() : false;
    }
    aValidType = isSymbol(a);
    bValidType = isSymbol(b);
    if (aValidType || bValidType) {
        return a === b;
    }
    aValidType = isArray(a);
    bValidType = isArray(b);
    if (aValidType || bValidType) {
        return aValidType && bValidType ? looseCompareArrays(a, b) : false;
    }
    aValidType = isObject(a);
    bValidType = isObject(b);
    if (aValidType || bValidType) {
        if (!aValidType || !bValidType) {
            return false;
        }
        const aKeysCount = Object.keys(a).length;
        const bKeysCount = Object.keys(b).length;
        if (aKeysCount !== bKeysCount) {
            return false;
        }
        for (const key in a) {
            const aHasKey = a.hasOwnProperty(key);
            const bHasKey = b.hasOwnProperty(key);
            if (
                (aHasKey && !bHasKey) ||
                (!aHasKey && bHasKey) ||
                !looseEqual(a[key], b[key])
            ) {
                return false;
            }
        }
    }
    return String(a) === String(b);
}
function looseIndexOf(arr, val) {
    return arr.findIndex((item) => looseEqual(item, val));
}
const isRef$1 = (val) => {
    return !!(val && val["__v_isRef"] === true);
};
const toDisplayString = (val) => {
    return isString(val)
        ? val
        : val == null
          ? ""
          : isArray(val) ||
              (isObject(val) &&
                  (val.toString === objectToString ||
                      !isFunction(val.toString)))
            ? isRef$1(val)
                ? toDisplayString(val.value)
                : JSON.stringify(val, replacer, 2)
            : String(val);
};
const replacer = (_key, val) => {
    if (isRef$1(val)) {
        return replacer(_key, val.value);
    } else if (isMap(val)) {
        return {
            [`Map(${val.size})`]: [...val.entries()].reduce(
                (entries, [key, val2], i) => {
                    entries[stringifySymbol(key, i) + " =>"] = val2;
                    return entries;
                },
                {},
            ),
        };
    } else if (isSet(val)) {
        return {
            [`Set(${val.size})`]: [...val.values()].map((v) =>
                stringifySymbol(v),
            ),
        };
    } else if (isSymbol(val)) {
        return stringifySymbol(val);
    } else if (isObject(val) && !isArray(val) && !isPlainObject$1(val)) {
        return String(val);
    }
    return val;
};
const stringifySymbol = (v, i = "") => {
    var _a;
    return (
        // Symbol.description in es2019+ so we need to cast here to pass
        // the lib: es2016 check
        isSymbol(v) ? `Symbol(${(_a = v.description) != null ? _a : i})` : v
    );
};
/**
 * @vue/reactivity v3.5.13
 * (c) 2018-present Yuxi (Evan) You and Vue contributors
 * @license MIT
 **/
let activeEffectScope;
class EffectScope {
    constructor(detached = false) {
        this.detached = detached;
        this._active = true;
        this.effects = [];
        this.cleanups = [];
        this._isPaused = false;
        this.parent = activeEffectScope;
        if (!detached && activeEffectScope) {
            this.index =
                (
                    activeEffectScope.scopes || (activeEffectScope.scopes = [])
                ).push(this) - 1;
        }
    }
    get active() {
        return this._active;
    }
    pause() {
        if (this._active) {
            this._isPaused = true;
            let i, l;
            if (this.scopes) {
                for (i = 0, l = this.scopes.length; i < l; i++) {
                    this.scopes[i].pause();
                }
            }
            for (i = 0, l = this.effects.length; i < l; i++) {
                this.effects[i].pause();
            }
        }
    }
    /**
     * Resumes the effect scope, including all child scopes and effects.
     */
    resume() {
        if (this._active) {
            if (this._isPaused) {
                this._isPaused = false;
                let i, l;
                if (this.scopes) {
                    for (i = 0, l = this.scopes.length; i < l; i++) {
                        this.scopes[i].resume();
                    }
                }
                for (i = 0, l = this.effects.length; i < l; i++) {
                    this.effects[i].resume();
                }
            }
        }
    }
    run(fn) {
        if (this._active) {
            const currentEffectScope = activeEffectScope;
            try {
                activeEffectScope = this;
                return fn();
            } finally {
                activeEffectScope = currentEffectScope;
            }
        }
    }
    /**
     * This should only be called on non-detached scopes
     * @internal
     */
    on() {
        activeEffectScope = this;
    }
    /**
     * This should only be called on non-detached scopes
     * @internal
     */
    off() {
        activeEffectScope = this.parent;
    }
    stop(fromParent) {
        if (this._active) {
            this._active = false;
            let i, l;
            for (i = 0, l = this.effects.length; i < l; i++) {
                this.effects[i].stop();
            }
            this.effects.length = 0;
            for (i = 0, l = this.cleanups.length; i < l; i++) {
                this.cleanups[i]();
            }
            this.cleanups.length = 0;
            if (this.scopes) {
                for (i = 0, l = this.scopes.length; i < l; i++) {
                    this.scopes[i].stop(true);
                }
                this.scopes.length = 0;
            }
            if (!this.detached && this.parent && !fromParent) {
                const last = this.parent.scopes.pop();
                if (last && last !== this) {
                    this.parent.scopes[this.index] = last;
                    last.index = this.index;
                }
            }
            this.parent = void 0;
        }
    }
}
function effectScope(detached) {
    return new EffectScope(detached);
}
function getCurrentScope() {
    return activeEffectScope;
}
function onScopeDispose(fn, failSilently = false) {
    if (activeEffectScope) {
        activeEffectScope.cleanups.push(fn);
    }
}
let activeSub;
const pausedQueueEffects = /* @__PURE__ */ new WeakSet();
class ReactiveEffect {
    constructor(fn) {
        this.fn = fn;
        this.deps = void 0;
        this.depsTail = void 0;
        this.flags = 1 | 4;
        this.next = void 0;
        this.cleanup = void 0;
        this.scheduler = void 0;
        if (activeEffectScope && activeEffectScope.active) {
            activeEffectScope.effects.push(this);
        }
    }
    pause() {
        this.flags |= 64;
    }
    resume() {
        if (this.flags & 64) {
            this.flags &= -65;
            if (pausedQueueEffects.has(this)) {
                pausedQueueEffects.delete(this);
                this.trigger();
            }
        }
    }
    /**
     * @internal
     */
    notify() {
        if (this.flags & 2 && !(this.flags & 32)) {
            return;
        }
        if (!(this.flags & 8)) {
            batch(this);
        }
    }
    run() {
        if (!(this.flags & 1)) {
            return this.fn();
        }
        this.flags |= 2;
        cleanupEffect(this);
        prepareDeps(this);
        const prevEffect = activeSub;
        const prevShouldTrack = shouldTrack;
        activeSub = this;
        shouldTrack = true;
        try {
            return this.fn();
        } finally {
            cleanupDeps(this);
            activeSub = prevEffect;
            shouldTrack = prevShouldTrack;
            this.flags &= -3;
        }
    }
    stop() {
        if (this.flags & 1) {
            for (let link = this.deps; link; link = link.nextDep) {
                removeSub(link);
            }
            this.deps = this.depsTail = void 0;
            cleanupEffect(this);
            this.onStop && this.onStop();
            this.flags &= -2;
        }
    }
    trigger() {
        if (this.flags & 64) {
            pausedQueueEffects.add(this);
        } else if (this.scheduler) {
            this.scheduler();
        } else {
            this.runIfDirty();
        }
    }
    /**
     * @internal
     */
    runIfDirty() {
        if (isDirty(this)) {
            this.run();
        }
    }
    get dirty() {
        return isDirty(this);
    }
}
let batchDepth = 0;
let batchedSub;
let batchedComputed;
function batch(sub, isComputed2 = false) {
    sub.flags |= 8;
    if (isComputed2) {
        sub.next = batchedComputed;
        batchedComputed = sub;
        return;
    }
    sub.next = batchedSub;
    batchedSub = sub;
}
function startBatch() {
    batchDepth++;
}
function endBatch() {
    if (--batchDepth > 0) {
        return;
    }
    if (batchedComputed) {
        let e = batchedComputed;
        batchedComputed = void 0;
        while (e) {
            const next = e.next;
            e.next = void 0;
            e.flags &= -9;
            e = next;
        }
    }
    let error;
    while (batchedSub) {
        let e = batchedSub;
        batchedSub = void 0;
        while (e) {
            const next = e.next;
            e.next = void 0;
            e.flags &= -9;
            if (e.flags & 1) {
                try {
                    e.trigger();
                } catch (err) {
                    if (!error) error = err;
                }
            }
            e = next;
        }
    }
    if (error) throw error;
}
function prepareDeps(sub) {
    for (let link = sub.deps; link; link = link.nextDep) {
        link.version = -1;
        link.prevActiveLink = link.dep.activeLink;
        link.dep.activeLink = link;
    }
}
function cleanupDeps(sub) {
    let head;
    let tail = sub.depsTail;
    let link = tail;
    while (link) {
        const prev = link.prevDep;
        if (link.version === -1) {
            if (link === tail) tail = prev;
            removeSub(link);
            removeDep(link);
        } else {
            head = link;
        }
        link.dep.activeLink = link.prevActiveLink;
        link.prevActiveLink = void 0;
        link = prev;
    }
    sub.deps = head;
    sub.depsTail = tail;
}
function isDirty(sub) {
    for (let link = sub.deps; link; link = link.nextDep) {
        if (
            link.dep.version !== link.version ||
            (link.dep.computed &&
                (refreshComputed(link.dep.computed) ||
                    link.dep.version !== link.version))
        ) {
            return true;
        }
    }
    if (sub._dirty) {
        return true;
    }
    return false;
}
function refreshComputed(computed2) {
    if (computed2.flags & 4 && !(computed2.flags & 16)) {
        return;
    }
    computed2.flags &= -17;
    if (computed2.globalVersion === globalVersion) {
        return;
    }
    computed2.globalVersion = globalVersion;
    const dep = computed2.dep;
    computed2.flags |= 2;
    if (
        dep.version > 0 &&
        !computed2.isSSR &&
        computed2.deps &&
        !isDirty(computed2)
    ) {
        computed2.flags &= -3;
        return;
    }
    const prevSub = activeSub;
    const prevShouldTrack = shouldTrack;
    activeSub = computed2;
    shouldTrack = true;
    try {
        prepareDeps(computed2);
        const value = computed2.fn(computed2._value);
        if (dep.version === 0 || hasChanged(value, computed2._value)) {
            computed2._value = value;
            dep.version++;
        }
    } catch (err) {
        dep.version++;
        throw err;
    } finally {
        activeSub = prevSub;
        shouldTrack = prevShouldTrack;
        cleanupDeps(computed2);
        computed2.flags &= -3;
    }
}
function removeSub(link, soft = false) {
    const { dep, prevSub, nextSub } = link;
    if (prevSub) {
        prevSub.nextSub = nextSub;
        link.prevSub = void 0;
    }
    if (nextSub) {
        nextSub.prevSub = prevSub;
        link.nextSub = void 0;
    }
    if (dep.subs === link) {
        dep.subs = prevSub;
        if (!prevSub && dep.computed) {
            dep.computed.flags &= -5;
            for (let l = dep.computed.deps; l; l = l.nextDep) {
                removeSub(l, true);
            }
        }
    }
    if (!soft && !--dep.sc && dep.map) {
        dep.map.delete(dep.key);
    }
}
function removeDep(link) {
    const { prevDep, nextDep } = link;
    if (prevDep) {
        prevDep.nextDep = nextDep;
        link.prevDep = void 0;
    }
    if (nextDep) {
        nextDep.prevDep = prevDep;
        link.nextDep = void 0;
    }
}
let shouldTrack = true;
const trackStack = [];
function pauseTracking() {
    trackStack.push(shouldTrack);
    shouldTrack = false;
}
function resetTracking() {
    const last = trackStack.pop();
    shouldTrack = last === void 0 ? true : last;
}
function cleanupEffect(e) {
    const { cleanup } = e;
    e.cleanup = void 0;
    if (cleanup) {
        const prevSub = activeSub;
        activeSub = void 0;
        try {
            cleanup();
        } finally {
            activeSub = prevSub;
        }
    }
}
let globalVersion = 0;
class Link {
    constructor(sub, dep) {
        this.sub = sub;
        this.dep = dep;
        this.version = dep.version;
        this.nextDep =
            this.prevDep =
            this.nextSub =
            this.prevSub =
            this.prevActiveLink =
                void 0;
    }
}
class Dep {
    constructor(computed2) {
        this.computed = computed2;
        this.version = 0;
        this.activeLink = void 0;
        this.subs = void 0;
        this.map = void 0;
        this.key = void 0;
        this.sc = 0;
    }
    track(debugInfo) {
        if (!activeSub || !shouldTrack || activeSub === this.computed) {
            return;
        }
        let link = this.activeLink;
        if (link === void 0 || link.sub !== activeSub) {
            link = this.activeLink = new Link(activeSub, this);
            if (!activeSub.deps) {
                activeSub.deps = activeSub.depsTail = link;
            } else {
                link.prevDep = activeSub.depsTail;
                activeSub.depsTail.nextDep = link;
                activeSub.depsTail = link;
            }
            addSub(link);
        } else if (link.version === -1) {
            link.version = this.version;
            if (link.nextDep) {
                const next = link.nextDep;
                next.prevDep = link.prevDep;
                if (link.prevDep) {
                    link.prevDep.nextDep = next;
                }
                link.prevDep = activeSub.depsTail;
                link.nextDep = void 0;
                activeSub.depsTail.nextDep = link;
                activeSub.depsTail = link;
                if (activeSub.deps === link) {
                    activeSub.deps = next;
                }
            }
        }
        return link;
    }
    trigger(debugInfo) {
        this.version++;
        globalVersion++;
        this.notify(debugInfo);
    }
    notify(debugInfo) {
        startBatch();
        try {
            if (false);
            for (let link = this.subs; link; link = link.prevSub) {
                if (link.sub.notify()) {
                    link.sub.dep.notify();
                }
            }
        } finally {
            endBatch();
        }
    }
}
function addSub(link) {
    link.dep.sc++;
    if (link.sub.flags & 4) {
        const computed2 = link.dep.computed;
        if (computed2 && !link.dep.subs) {
            computed2.flags |= 4 | 16;
            for (let l = computed2.deps; l; l = l.nextDep) {
                addSub(l);
            }
        }
        const currentTail = link.dep.subs;
        if (currentTail !== link) {
            link.prevSub = currentTail;
            if (currentTail) currentTail.nextSub = link;
        }
        link.dep.subs = link;
    }
}
const targetMap = /* @__PURE__ */ new WeakMap();
const ITERATE_KEY = Symbol("");
const MAP_KEY_ITERATE_KEY = Symbol("");
const ARRAY_ITERATE_KEY = Symbol("");
function track(target, type, key) {
    if (shouldTrack && activeSub) {
        let depsMap = targetMap.get(target);
        if (!depsMap) {
            targetMap.set(target, (depsMap = /* @__PURE__ */ new Map()));
        }
        let dep = depsMap.get(key);
        if (!dep) {
            depsMap.set(key, (dep = new Dep()));
            dep.map = depsMap;
            dep.key = key;
        }
        {
            dep.track();
        }
    }
}
function trigger(target, type, key, newValue, oldValue, oldTarget) {
    const depsMap = targetMap.get(target);
    if (!depsMap) {
        globalVersion++;
        return;
    }
    const run = (dep) => {
        if (dep) {
            {
                dep.trigger();
            }
        }
    };
    startBatch();
    if (type === "clear") {
        depsMap.forEach(run);
    } else {
        const targetIsArray = isArray(target);
        const isArrayIndex = targetIsArray && isIntegerKey(key);
        if (targetIsArray && key === "length") {
            const newLength = Number(newValue);
            depsMap.forEach((dep, key2) => {
                if (
                    key2 === "length" ||
                    key2 === ARRAY_ITERATE_KEY ||
                    (!isSymbol(key2) && key2 >= newLength)
                ) {
                    run(dep);
                }
            });
        } else {
            if (key !== void 0 || depsMap.has(void 0)) {
                run(depsMap.get(key));
            }
            if (isArrayIndex) {
                run(depsMap.get(ARRAY_ITERATE_KEY));
            }
            switch (type) {
                case "add":
                    if (!targetIsArray) {
                        run(depsMap.get(ITERATE_KEY));
                        if (isMap(target)) {
                            run(depsMap.get(MAP_KEY_ITERATE_KEY));
                        }
                    } else if (isArrayIndex) {
                        run(depsMap.get("length"));
                    }
                    break;
                case "delete":
                    if (!targetIsArray) {
                        run(depsMap.get(ITERATE_KEY));
                        if (isMap(target)) {
                            run(depsMap.get(MAP_KEY_ITERATE_KEY));
                        }
                    }
                    break;
                case "set":
                    if (isMap(target)) {
                        run(depsMap.get(ITERATE_KEY));
                    }
                    break;
            }
        }
    }
    endBatch();
}
function getDepFromReactive(object, key) {
    const depMap = targetMap.get(object);
    return depMap && depMap.get(key);
}
function reactiveReadArray(array) {
    const raw = toRaw(array);
    if (raw === array) return raw;
    track(raw, "iterate", ARRAY_ITERATE_KEY);
    return isShallow(array) ? raw : raw.map(toReactive);
}
function shallowReadArray(arr) {
    track((arr = toRaw(arr)), "iterate", ARRAY_ITERATE_KEY);
    return arr;
}
const arrayInstrumentations = {
    __proto__: null,
    [Symbol.iterator]() {
        return iterator(this, Symbol.iterator, toReactive);
    },
    concat(...args) {
        return reactiveReadArray(this).concat(
            ...args.map((x) => (isArray(x) ? reactiveReadArray(x) : x)),
        );
    },
    entries() {
        return iterator(this, "entries", (value) => {
            value[1] = toReactive(value[1]);
            return value;
        });
    },
    every(fn, thisArg) {
        return apply(this, "every", fn, thisArg, void 0, arguments);
    },
    filter(fn, thisArg) {
        return apply(
            this,
            "filter",
            fn,
            thisArg,
            (v) => v.map(toReactive),
            arguments,
        );
    },
    find(fn, thisArg) {
        return apply(this, "find", fn, thisArg, toReactive, arguments);
    },
    findIndex(fn, thisArg) {
        return apply(this, "findIndex", fn, thisArg, void 0, arguments);
    },
    findLast(fn, thisArg) {
        return apply(this, "findLast", fn, thisArg, toReactive, arguments);
    },
    findLastIndex(fn, thisArg) {
        return apply(this, "findLastIndex", fn, thisArg, void 0, arguments);
    },
    // flat, flatMap could benefit from ARRAY_ITERATE but are not straight-forward to implement
    forEach(fn, thisArg) {
        return apply(this, "forEach", fn, thisArg, void 0, arguments);
    },
    includes(...args) {
        return searchProxy(this, "includes", args);
    },
    indexOf(...args) {
        return searchProxy(this, "indexOf", args);
    },
    join(separator) {
        return reactiveReadArray(this).join(separator);
    },
    // keys() iterator only reads `length`, no optimisation required
    lastIndexOf(...args) {
        return searchProxy(this, "lastIndexOf", args);
    },
    map(fn, thisArg) {
        return apply(this, "map", fn, thisArg, void 0, arguments);
    },
    pop() {
        return noTracking(this, "pop");
    },
    push(...args) {
        return noTracking(this, "push", args);
    },
    reduce(fn, ...args) {
        return reduce(this, "reduce", fn, args);
    },
    reduceRight(fn, ...args) {
        return reduce(this, "reduceRight", fn, args);
    },
    shift() {
        return noTracking(this, "shift");
    },
    // slice could use ARRAY_ITERATE but also seems to beg for range tracking
    some(fn, thisArg) {
        return apply(this, "some", fn, thisArg, void 0, arguments);
    },
    splice(...args) {
        return noTracking(this, "splice", args);
    },
    toReversed() {
        return reactiveReadArray(this).toReversed();
    },
    toSorted(comparer) {
        return reactiveReadArray(this).toSorted(comparer);
    },
    toSpliced(...args) {
        return reactiveReadArray(this).toSpliced(...args);
    },
    unshift(...args) {
        return noTracking(this, "unshift", args);
    },
    values() {
        return iterator(this, "values", toReactive);
    },
};
function iterator(self2, method, wrapValue) {
    const arr = shallowReadArray(self2);
    const iter = arr[method]();
    if (arr !== self2 && !isShallow(self2)) {
        iter._next = iter.next;
        iter.next = () => {
            const result = iter._next();
            if (result.value) {
                result.value = wrapValue(result.value);
            }
            return result;
        };
    }
    return iter;
}
const arrayProto = Array.prototype;
function apply(self2, method, fn, thisArg, wrappedRetFn, args) {
    const arr = shallowReadArray(self2);
    const needsWrap = arr !== self2 && !isShallow(self2);
    const methodFn = arr[method];
    if (methodFn !== arrayProto[method]) {
        const result2 = methodFn.apply(self2, args);
        return needsWrap ? toReactive(result2) : result2;
    }
    let wrappedFn = fn;
    if (arr !== self2) {
        if (needsWrap) {
            wrappedFn = function (item, index) {
                return fn.call(this, toReactive(item), index, self2);
            };
        } else if (fn.length > 2) {
            wrappedFn = function (item, index) {
                return fn.call(this, item, index, self2);
            };
        }
    }
    const result = methodFn.call(arr, wrappedFn, thisArg);
    return needsWrap && wrappedRetFn ? wrappedRetFn(result) : result;
}
function reduce(self2, method, fn, args) {
    const arr = shallowReadArray(self2);
    let wrappedFn = fn;
    if (arr !== self2) {
        if (!isShallow(self2)) {
            wrappedFn = function (acc, item, index) {
                return fn.call(this, acc, toReactive(item), index, self2);
            };
        } else if (fn.length > 3) {
            wrappedFn = function (acc, item, index) {
                return fn.call(this, acc, item, index, self2);
            };
        }
    }
    return arr[method](wrappedFn, ...args);
}
function searchProxy(self2, method, args) {
    const arr = toRaw(self2);
    track(arr, "iterate", ARRAY_ITERATE_KEY);
    const res = arr[method](...args);
    if ((res === -1 || res === false) && isProxy(args[0])) {
        args[0] = toRaw(args[0]);
        return arr[method](...args);
    }
    return res;
}
function noTracking(self2, method, args = []) {
    pauseTracking();
    startBatch();
    const res = toRaw(self2)[method].apply(self2, args);
    endBatch();
    resetTracking();
    return res;
}
const isNonTrackableKeys = /* @__PURE__ */ makeMap(
    `__proto__,__v_isRef,__isVue`,
);
const builtInSymbols = new Set(
    /* @__PURE__ */ Object.getOwnPropertyNames(Symbol)
        .filter((key) => key !== "arguments" && key !== "caller")
        .map((key) => Symbol[key])
        .filter(isSymbol),
);
function hasOwnProperty(key) {
    if (!isSymbol(key)) key = String(key);
    const obj = toRaw(this);
    track(obj, "has", key);
    return obj.hasOwnProperty(key);
}
class BaseReactiveHandler {
    constructor(_isReadonly = false, _isShallow = false) {
        this._isReadonly = _isReadonly;
        this._isShallow = _isShallow;
    }
    get(target, key, receiver) {
        if (key === "__v_skip") return target["__v_skip"];
        const isReadonly2 = this._isReadonly,
            isShallow2 = this._isShallow;
        if (key === "__v_isReactive") {
            return !isReadonly2;
        } else if (key === "__v_isReadonly") {
            return isReadonly2;
        } else if (key === "__v_isShallow") {
            return isShallow2;
        } else if (key === "__v_raw") {
            if (
                receiver ===
                    (isReadonly2
                        ? isShallow2
                            ? shallowReadonlyMap
                            : readonlyMap
                        : isShallow2
                          ? shallowReactiveMap
                          : reactiveMap
                    ).get(target) || // receiver is not the reactive proxy, but has the same prototype
                // this means the receiver is a user proxy of the reactive proxy
                Object.getPrototypeOf(target) ===
                    Object.getPrototypeOf(receiver)
            ) {
                return target;
            }
            return;
        }
        const targetIsArray = isArray(target);
        if (!isReadonly2) {
            let fn;
            if (targetIsArray && (fn = arrayInstrumentations[key])) {
                return fn;
            }
            if (key === "hasOwnProperty") {
                return hasOwnProperty;
            }
        }
        const res = Reflect.get(
            target,
            key,
            // if this is a proxy wrapping a ref, return methods using the raw ref
            // as receiver so that we don't have to call `toRaw` on the ref in all
            // its class methods
            isRef(target) ? target : receiver,
        );
        if (isSymbol(key) ? builtInSymbols.has(key) : isNonTrackableKeys(key)) {
            return res;
        }
        if (!isReadonly2) {
            track(target, "get", key);
        }
        if (isShallow2) {
            return res;
        }
        if (isRef(res)) {
            return targetIsArray && isIntegerKey(key) ? res : res.value;
        }
        if (isObject(res)) {
            return isReadonly2 ? readonly(res) : reactive(res);
        }
        return res;
    }
}
class MutableReactiveHandler extends BaseReactiveHandler {
    constructor(isShallow2 = false) {
        super(false, isShallow2);
    }
    set(target, key, value, receiver) {
        let oldValue = target[key];
        if (!this._isShallow) {
            const isOldValueReadonly = isReadonly(oldValue);
            if (!isShallow(value) && !isReadonly(value)) {
                oldValue = toRaw(oldValue);
                value = toRaw(value);
            }
            if (!isArray(target) && isRef(oldValue) && !isRef(value)) {
                if (isOldValueReadonly) {
                    return false;
                } else {
                    oldValue.value = value;
                    return true;
                }
            }
        }
        const hadKey =
            isArray(target) && isIntegerKey(key)
                ? Number(key) < target.length
                : hasOwn(target, key);
        const result = Reflect.set(
            target,
            key,
            value,
            isRef(target) ? target : receiver,
        );
        if (target === toRaw(receiver)) {
            if (!hadKey) {
                trigger(target, "add", key, value);
            } else if (hasChanged(value, oldValue)) {
                trigger(target, "set", key, value);
            }
        }
        return result;
    }
    deleteProperty(target, key) {
        const hadKey = hasOwn(target, key);
        target[key];
        const result = Reflect.deleteProperty(target, key);
        if (result && hadKey) {
            trigger(target, "delete", key, void 0);
        }
        return result;
    }
    has(target, key) {
        const result = Reflect.has(target, key);
        if (!isSymbol(key) || !builtInSymbols.has(key)) {
            track(target, "has", key);
        }
        return result;
    }
    ownKeys(target) {
        track(target, "iterate", isArray(target) ? "length" : ITERATE_KEY);
        return Reflect.ownKeys(target);
    }
}
class ReadonlyReactiveHandler extends BaseReactiveHandler {
    constructor(isShallow2 = false) {
        super(true, isShallow2);
    }
    set(target, key) {
        return true;
    }
    deleteProperty(target, key) {
        return true;
    }
}
const mutableHandlers = /* @__PURE__ */ new MutableReactiveHandler();
const readonlyHandlers = /* @__PURE__ */ new ReadonlyReactiveHandler();
const shallowReactiveHandlers = /* @__PURE__ */ new MutableReactiveHandler(
    true,
);
const shallowReadonlyHandlers = /* @__PURE__ */ new ReadonlyReactiveHandler(
    true,
);
const toShallow = (value) => value;
const getProto = (v) => Reflect.getPrototypeOf(v);
function createIterableMethod(method, isReadonly2, isShallow2) {
    return function (...args) {
        const target = this["__v_raw"];
        const rawTarget = toRaw(target);
        const targetIsMap = isMap(rawTarget);
        const isPair =
            method === "entries" || (method === Symbol.iterator && targetIsMap);
        const isKeyOnly = method === "keys" && targetIsMap;
        const innerIterator = target[method](...args);
        const wrap = isShallow2
            ? toShallow
            : isReadonly2
              ? toReadonly
              : toReactive;
        !isReadonly2 &&
            track(
                rawTarget,
                "iterate",
                isKeyOnly ? MAP_KEY_ITERATE_KEY : ITERATE_KEY,
            );
        return {
            // iterator protocol
            next() {
                const { value, done } = innerIterator.next();
                return done
                    ? { value, done }
                    : {
                          value: isPair
                              ? [wrap(value[0]), wrap(value[1])]
                              : wrap(value),
                          done,
                      };
            },
            // iterable protocol
            [Symbol.iterator]() {
                return this;
            },
        };
    };
}
function createReadonlyMethod(type) {
    return function (...args) {
        return type === "delete" ? false : type === "clear" ? void 0 : this;
    };
}
function createInstrumentations(readonly2, shallow) {
    const instrumentations = {
        get(key) {
            const target = this["__v_raw"];
            const rawTarget = toRaw(target);
            const rawKey = toRaw(key);
            if (!readonly2) {
                if (hasChanged(key, rawKey)) {
                    track(rawTarget, "get", key);
                }
                track(rawTarget, "get", rawKey);
            }
            const { has } = getProto(rawTarget);
            const wrap = shallow
                ? toShallow
                : readonly2
                  ? toReadonly
                  : toReactive;
            if (has.call(rawTarget, key)) {
                return wrap(target.get(key));
            } else if (has.call(rawTarget, rawKey)) {
                return wrap(target.get(rawKey));
            } else if (target !== rawTarget) {
                target.get(key);
            }
        },
        get size() {
            const target = this["__v_raw"];
            !readonly2 && track(toRaw(target), "iterate", ITERATE_KEY);
            return Reflect.get(target, "size", target);
        },
        has(key) {
            const target = this["__v_raw"];
            const rawTarget = toRaw(target);
            const rawKey = toRaw(key);
            if (!readonly2) {
                if (hasChanged(key, rawKey)) {
                    track(rawTarget, "has", key);
                }
                track(rawTarget, "has", rawKey);
            }
            return key === rawKey
                ? target.has(key)
                : target.has(key) || target.has(rawKey);
        },
        forEach(callback, thisArg) {
            const observed = this;
            const target = observed["__v_raw"];
            const rawTarget = toRaw(target);
            const wrap = shallow
                ? toShallow
                : readonly2
                  ? toReadonly
                  : toReactive;
            !readonly2 && track(rawTarget, "iterate", ITERATE_KEY);
            return target.forEach((value, key) => {
                return callback.call(thisArg, wrap(value), wrap(key), observed);
            });
        },
    };
    extend(
        instrumentations,
        readonly2
            ? {
                  add: createReadonlyMethod("add"),
                  set: createReadonlyMethod("set"),
                  delete: createReadonlyMethod("delete"),
                  clear: createReadonlyMethod("clear"),
              }
            : {
                  add(value) {
                      if (!shallow && !isShallow(value) && !isReadonly(value)) {
                          value = toRaw(value);
                      }
                      const target = toRaw(this);
                      const proto = getProto(target);
                      const hadKey = proto.has.call(target, value);
                      if (!hadKey) {
                          target.add(value);
                          trigger(target, "add", value, value);
                      }
                      return this;
                  },
                  set(key, value) {
                      if (!shallow && !isShallow(value) && !isReadonly(value)) {
                          value = toRaw(value);
                      }
                      const target = toRaw(this);
                      const { has, get: get2 } = getProto(target);
                      let hadKey = has.call(target, key);
                      if (!hadKey) {
                          key = toRaw(key);
                          hadKey = has.call(target, key);
                      }
                      const oldValue = get2.call(target, key);
                      target.set(key, value);
                      if (!hadKey) {
                          trigger(target, "add", key, value);
                      } else if (hasChanged(value, oldValue)) {
                          trigger(target, "set", key, value);
                      }
                      return this;
                  },
                  delete(key) {
                      const target = toRaw(this);
                      const { has, get: get2 } = getProto(target);
                      let hadKey = has.call(target, key);
                      if (!hadKey) {
                          key = toRaw(key);
                          hadKey = has.call(target, key);
                      }
                      get2 ? get2.call(target, key) : void 0;
                      const result = target.delete(key);
                      if (hadKey) {
                          trigger(target, "delete", key, void 0);
                      }
                      return result;
                  },
                  clear() {
                      const target = toRaw(this);
                      const hadItems = target.size !== 0;
                      const result = target.clear();
                      if (hadItems) {
                          trigger(target, "clear", void 0, void 0);
                      }
                      return result;
                  },
              },
    );
    const iteratorMethods = ["keys", "values", "entries", Symbol.iterator];
    iteratorMethods.forEach((method) => {
        instrumentations[method] = createIterableMethod(
            method,
            readonly2,
            shallow,
        );
    });
    return instrumentations;
}
function createInstrumentationGetter(isReadonly2, shallow) {
    const instrumentations = createInstrumentations(isReadonly2, shallow);
    return (target, key, receiver) => {
        if (key === "__v_isReactive") {
            return !isReadonly2;
        } else if (key === "__v_isReadonly") {
            return isReadonly2;
        } else if (key === "__v_raw") {
            return target;
        }
        return Reflect.get(
            hasOwn(instrumentations, key) && key in target
                ? instrumentations
                : target,
            key,
            receiver,
        );
    };
}
const mutableCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(false, false),
};
const shallowCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(false, true),
};
const readonlyCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(true, false),
};
const shallowReadonlyCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(true, true),
};
const reactiveMap = /* @__PURE__ */ new WeakMap();
const shallowReactiveMap = /* @__PURE__ */ new WeakMap();
const readonlyMap = /* @__PURE__ */ new WeakMap();
const shallowReadonlyMap = /* @__PURE__ */ new WeakMap();
function targetTypeMap(rawType) {
    switch (rawType) {
        case "Object":
        case "Array":
            return 1;
        case "Map":
        case "Set":
        case "WeakMap":
        case "WeakSet":
            return 2;
        default:
            return 0;
    }
}
function getTargetType(value) {
    return value["__v_skip"] || !Object.isExtensible(value)
        ? 0
        : targetTypeMap(toRawType(value));
}
function reactive(target) {
    if (isReadonly(target)) {
        return target;
    }
    return createReactiveObject(
        target,
        false,
        mutableHandlers,
        mutableCollectionHandlers,
        reactiveMap,
    );
}
function shallowReactive(target) {
    return createReactiveObject(
        target,
        false,
        shallowReactiveHandlers,
        shallowCollectionHandlers,
        shallowReactiveMap,
    );
}
function readonly(target) {
    return createReactiveObject(
        target,
        true,
        readonlyHandlers,
        readonlyCollectionHandlers,
        readonlyMap,
    );
}
function shallowReadonly(target) {
    return createReactiveObject(
        target,
        true,
        shallowReadonlyHandlers,
        shallowReadonlyCollectionHandlers,
        shallowReadonlyMap,
    );
}
function createReactiveObject(
    target,
    isReadonly2,
    baseHandlers,
    collectionHandlers,
    proxyMap,
) {
    if (!isObject(target)) {
        return target;
    }
    if (target["__v_raw"] && !(isReadonly2 && target["__v_isReactive"])) {
        return target;
    }
    const existingProxy = proxyMap.get(target);
    if (existingProxy) {
        return existingProxy;
    }
    const targetType = getTargetType(target);
    if (targetType === 0) {
        return target;
    }
    const proxy = new Proxy(
        target,
        targetType === 2 ? collectionHandlers : baseHandlers,
    );
    proxyMap.set(target, proxy);
    return proxy;
}
function isReactive(value) {
    if (isReadonly(value)) {
        return isReactive(value["__v_raw"]);
    }
    return !!(value && value["__v_isReactive"]);
}
function isReadonly(value) {
    return !!(value && value["__v_isReadonly"]);
}
function isShallow(value) {
    return !!(value && value["__v_isShallow"]);
}
function isProxy(value) {
    return value ? !!value["__v_raw"] : false;
}
function toRaw(observed) {
    const raw = observed && observed["__v_raw"];
    return raw ? toRaw(raw) : observed;
}
function markRaw(value) {
    if (!hasOwn(value, "__v_skip") && Object.isExtensible(value)) {
        def(value, "__v_skip", true);
    }
    return value;
}
const toReactive = (value) => (isObject(value) ? reactive(value) : value);
const toReadonly = (value) => (isObject(value) ? readonly(value) : value);
function isRef(r) {
    return r ? r["__v_isRef"] === true : false;
}
function ref(value) {
    return createRef(value, false);
}
function createRef(rawValue, shallow) {
    if (isRef(rawValue)) {
        return rawValue;
    }
    return new RefImpl(rawValue, shallow);
}
class RefImpl {
    constructor(value, isShallow2) {
        this.dep = new Dep();
        this["__v_isRef"] = true;
        this["__v_isShallow"] = false;
        this._rawValue = isShallow2 ? value : toRaw(value);
        this._value = isShallow2 ? value : toReactive(value);
        this["__v_isShallow"] = isShallow2;
    }
    get value() {
        {
            this.dep.track();
        }
        return this._value;
    }
    set value(newValue) {
        const oldValue = this._rawValue;
        const useDirectValue =
            this["__v_isShallow"] ||
            isShallow(newValue) ||
            isReadonly(newValue);
        newValue = useDirectValue ? newValue : toRaw(newValue);
        if (hasChanged(newValue, oldValue)) {
            this._rawValue = newValue;
            this._value = useDirectValue ? newValue : toReactive(newValue);
            {
                this.dep.trigger();
            }
        }
    }
}
function unref(ref2) {
    return isRef(ref2) ? ref2.value : ref2;
}
const shallowUnwrapHandlers = {
    get: (target, key, receiver) =>
        key === "__v_raw" ? target : unref(Reflect.get(target, key, receiver)),
    set: (target, key, value, receiver) => {
        const oldValue = target[key];
        if (isRef(oldValue) && !isRef(value)) {
            oldValue.value = value;
            return true;
        } else {
            return Reflect.set(target, key, value, receiver);
        }
    },
};
function proxyRefs(objectWithRefs) {
    return isReactive(objectWithRefs)
        ? objectWithRefs
        : new Proxy(objectWithRefs, shallowUnwrapHandlers);
}
function toRefs(object) {
    const ret = isArray(object) ? new Array(object.length) : {};
    for (const key in object) {
        ret[key] = propertyToRef(object, key);
    }
    return ret;
}
class ObjectRefImpl {
    constructor(_object, _key, _defaultValue) {
        this._object = _object;
        this._key = _key;
        this._defaultValue = _defaultValue;
        this["__v_isRef"] = true;
        this._value = void 0;
    }
    get value() {
        const val = this._object[this._key];
        return (this._value = val === void 0 ? this._defaultValue : val);
    }
    set value(newVal) {
        this._object[this._key] = newVal;
    }
    get dep() {
        return getDepFromReactive(toRaw(this._object), this._key);
    }
}
function propertyToRef(source, key, defaultValue) {
    const val = source[key];
    return isRef(val) ? val : new ObjectRefImpl(source, key, defaultValue);
}
class ComputedRefImpl {
    constructor(fn, setter, isSSR) {
        this.fn = fn;
        this.setter = setter;
        this._value = void 0;
        this.dep = new Dep(this);
        this.__v_isRef = true;
        this.deps = void 0;
        this.depsTail = void 0;
        this.flags = 16;
        this.globalVersion = globalVersion - 1;
        this.next = void 0;
        this.effect = this;
        this["__v_isReadonly"] = !setter;
        this.isSSR = isSSR;
    }
    /**
     * @internal
     */
    notify() {
        this.flags |= 16;
        if (
            !(this.flags & 8) && // avoid infinite self recursion
            activeSub !== this
        ) {
            batch(this, true);
            return true;
        }
    }
    get value() {
        const link = this.dep.track();
        refreshComputed(this);
        if (link) {
            link.version = this.dep.version;
        }
        return this._value;
    }
    set value(newValue) {
        if (this.setter) {
            this.setter(newValue);
        }
    }
}
function computed$1(getterOrOptions, debugOptions, isSSR = false) {
    let getter;
    let setter;
    if (isFunction(getterOrOptions)) {
        getter = getterOrOptions;
    } else {
        getter = getterOrOptions.get;
        setter = getterOrOptions.set;
    }
    const cRef = new ComputedRefImpl(getter, setter, isSSR);
    return cRef;
}
const INITIAL_WATCHER_VALUE = {};
const cleanupMap = /* @__PURE__ */ new WeakMap();
let activeWatcher = void 0;
function onWatcherCleanup(
    cleanupFn,
    failSilently = false,
    owner = activeWatcher,
) {
    if (owner) {
        let cleanups = cleanupMap.get(owner);
        if (!cleanups) cleanupMap.set(owner, (cleanups = []));
        cleanups.push(cleanupFn);
    }
}
function watch$1(source, cb, options = EMPTY_OBJ) {
    const { immediate, deep, once, scheduler, augmentJob, call } = options;
    const reactiveGetter = (source2) => {
        if (deep) return source2;
        if (isShallow(source2) || deep === false || deep === 0)
            return traverse(source2, 1);
        return traverse(source2);
    };
    let effect2;
    let getter;
    let cleanup;
    let boundCleanup;
    let forceTrigger = false;
    let isMultiSource = false;
    if (isRef(source)) {
        getter = () => source.value;
        forceTrigger = isShallow(source);
    } else if (isReactive(source)) {
        getter = () => reactiveGetter(source);
        forceTrigger = true;
    } else if (isArray(source)) {
        isMultiSource = true;
        forceTrigger = source.some((s) => isReactive(s) || isShallow(s));
        getter = () =>
            source.map((s) => {
                if (isRef(s)) {
                    return s.value;
                } else if (isReactive(s)) {
                    return reactiveGetter(s);
                } else if (isFunction(s)) {
                    return call ? call(s, 2) : s();
                } else;
            });
    } else if (isFunction(source)) {
        if (cb) {
            getter = call ? () => call(source, 2) : source;
        } else {
            getter = () => {
                if (cleanup) {
                    pauseTracking();
                    try {
                        cleanup();
                    } finally {
                        resetTracking();
                    }
                }
                const currentEffect = activeWatcher;
                activeWatcher = effect2;
                try {
                    return call
                        ? call(source, 3, [boundCleanup])
                        : source(boundCleanup);
                } finally {
                    activeWatcher = currentEffect;
                }
            };
        }
    } else {
        getter = NOOP;
    }
    if (cb && deep) {
        const baseGetter = getter;
        const depth = deep === true ? Infinity : deep;
        getter = () => traverse(baseGetter(), depth);
    }
    const scope = getCurrentScope();
    const watchHandle = () => {
        effect2.stop();
        if (scope && scope.active) {
            remove(scope.effects, effect2);
        }
    };
    if (once && cb) {
        const _cb = cb;
        cb = (...args) => {
            _cb(...args);
            watchHandle();
        };
    }
    let oldValue = isMultiSource
        ? new Array(source.length).fill(INITIAL_WATCHER_VALUE)
        : INITIAL_WATCHER_VALUE;
    const job = (immediateFirstRun) => {
        if (!(effect2.flags & 1) || (!effect2.dirty && !immediateFirstRun)) {
            return;
        }
        if (cb) {
            const newValue = effect2.run();
            if (
                deep ||
                forceTrigger ||
                (isMultiSource
                    ? newValue.some((v, i) => hasChanged(v, oldValue[i]))
                    : hasChanged(newValue, oldValue))
            ) {
                if (cleanup) {
                    cleanup();
                }
                const currentWatcher = activeWatcher;
                activeWatcher = effect2;
                try {
                    const args = [
                        newValue,
                        // pass undefined as the old value when it's changed for the first time
                        oldValue === INITIAL_WATCHER_VALUE
                            ? void 0
                            : isMultiSource &&
                                oldValue[0] === INITIAL_WATCHER_VALUE
                              ? []
                              : oldValue,
                        boundCleanup,
                    ];
                    call
                        ? call(cb, 3, args)
                        : // @ts-expect-error
                          cb(...args);
                    oldValue = newValue;
                } finally {
                    activeWatcher = currentWatcher;
                }
            }
        } else {
            effect2.run();
        }
    };
    if (augmentJob) {
        augmentJob(job);
    }
    effect2 = new ReactiveEffect(getter);
    effect2.scheduler = scheduler ? () => scheduler(job, false) : job;
    boundCleanup = (fn) => onWatcherCleanup(fn, false, effect2);
    cleanup = effect2.onStop = () => {
        const cleanups = cleanupMap.get(effect2);
        if (cleanups) {
            if (call) {
                call(cleanups, 4);
            } else {
                for (const cleanup2 of cleanups) cleanup2();
            }
            cleanupMap.delete(effect2);
        }
    };
    if (cb) {
        if (immediate) {
            job(true);
        } else {
            oldValue = effect2.run();
        }
    } else if (scheduler) {
        scheduler(job.bind(null, true), true);
    } else {
        effect2.run();
    }
    watchHandle.pause = effect2.pause.bind(effect2);
    watchHandle.resume = effect2.resume.bind(effect2);
    watchHandle.stop = watchHandle;
    return watchHandle;
}
function traverse(value, depth = Infinity, seen) {
    if (depth <= 0 || !isObject(value) || value["__v_skip"]) {
        return value;
    }
    seen = seen || /* @__PURE__ */ new Set();
    if (seen.has(value)) {
        return value;
    }
    seen.add(value);
    depth--;
    if (isRef(value)) {
        traverse(value.value, depth, seen);
    } else if (isArray(value)) {
        for (let i = 0; i < value.length; i++) {
            traverse(value[i], depth, seen);
        }
    } else if (isSet(value) || isMap(value)) {
        value.forEach((v) => {
            traverse(v, depth, seen);
        });
    } else if (isPlainObject$1(value)) {
        for (const key in value) {
            traverse(value[key], depth, seen);
        }
        for (const key of Object.getOwnPropertySymbols(value)) {
            if (Object.prototype.propertyIsEnumerable.call(value, key)) {
                traverse(value[key], depth, seen);
            }
        }
    }
    return value;
}
/**
 * @vue/runtime-core v3.5.13
 * (c) 2018-present Yuxi (Evan) You and Vue contributors
 * @license MIT
 **/
const stack = [];
let isWarning = false;
function warn$1(msg, ...args) {
    if (isWarning) return;
    isWarning = true;
    pauseTracking();
    const instance = stack.length ? stack[stack.length - 1].component : null;
    const appWarnHandler = instance && instance.appContext.config.warnHandler;
    const trace = getComponentTrace();
    if (appWarnHandler) {
        callWithErrorHandling(appWarnHandler, instance, 11, [
            // eslint-disable-next-line no-restricted-syntax
            msg +
                args
                    .map((a) => {
                        var _a, _b;
                        return (_b =
                            (_a = a.toString) == null ? void 0 : _a.call(a)) !=
                            null
                            ? _b
                            : JSON.stringify(a);
                    })
                    .join(""),
            instance && instance.proxy,
            trace
                .map(
                    ({ vnode }) =>
                        `at <${formatComponentName(instance, vnode.type)}>`,
                )
                .join("\n"),
            trace,
        ]);
    } else {
        const warnArgs = [`[Vue warn]: ${msg}`, ...args];
        if (
            trace.length && // avoid spamming console during tests
            true
        ) {
            warnArgs.push(
                `
`,
                ...formatTrace(trace),
            );
        }
        console.warn(...warnArgs);
    }
    resetTracking();
    isWarning = false;
}
function getComponentTrace() {
    let currentVNode = stack[stack.length - 1];
    if (!currentVNode) {
        return [];
    }
    const normalizedStack = [];
    while (currentVNode) {
        const last = normalizedStack[0];
        if (last && last.vnode === currentVNode) {
            last.recurseCount++;
        } else {
            normalizedStack.push({
                vnode: currentVNode,
                recurseCount: 0,
            });
        }
        const parentInstance =
            currentVNode.component && currentVNode.component.parent;
        currentVNode = parentInstance && parentInstance.vnode;
    }
    return normalizedStack;
}
function formatTrace(trace) {
    const logs = [];
    trace.forEach((entry, i) => {
        logs.push(
            ...(i === 0
                ? []
                : [
                      `
`,
                  ]),
            ...formatTraceEntry(entry),
        );
    });
    return logs;
}
function formatTraceEntry({ vnode, recurseCount }) {
    const postfix =
        recurseCount > 0 ? `... (${recurseCount} recursive calls)` : ``;
    const isRoot = vnode.component ? vnode.component.parent == null : false;
    const open = ` at <${formatComponentName(
        vnode.component,
        vnode.type,
        isRoot,
    )}`;
    const close = `>` + postfix;
    return vnode.props
        ? [open, ...formatProps(vnode.props), close]
        : [open + close];
}
function formatProps(props) {
    const res = [];
    const keys = Object.keys(props);
    keys.slice(0, 3).forEach((key) => {
        res.push(...formatProp(key, props[key]));
    });
    if (keys.length > 3) {
        res.push(` ...`);
    }
    return res;
}
function formatProp(key, value, raw) {
    if (isString(value)) {
        value = JSON.stringify(value);
        return raw ? value : [`${key}=${value}`];
    } else if (
        typeof value === "number" ||
        typeof value === "boolean" ||
        value == null
    ) {
        return raw ? value : [`${key}=${value}`];
    } else if (isRef(value)) {
        value = formatProp(key, toRaw(value.value), true);
        return raw ? value : [`${key}=Ref<`, value, `>`];
    } else if (isFunction(value)) {
        return [`${key}=fn${value.name ? `<${value.name}>` : ``}`];
    } else {
        value = toRaw(value);
        return raw ? value : [`${key}=`, value];
    }
}
function callWithErrorHandling(fn, instance, type, args) {
    try {
        return args ? fn(...args) : fn();
    } catch (err) {
        handleError(err, instance, type);
    }
}
function callWithAsyncErrorHandling(fn, instance, type, args) {
    if (isFunction(fn)) {
        const res = callWithErrorHandling(fn, instance, type, args);
        if (res && isPromise$1(res)) {
            res.catch((err) => {
                handleError(err, instance, type);
            });
        }
        return res;
    }
    if (isArray(fn)) {
        const values = [];
        for (let i = 0; i < fn.length; i++) {
            values.push(
                callWithAsyncErrorHandling(fn[i], instance, type, args),
            );
        }
        return values;
    }
}
function handleError(err, instance, type, throwInDev = true) {
    const contextVNode = instance ? instance.vnode : null;
    const { errorHandler, throwUnhandledErrorInProduction } =
        (instance && instance.appContext.config) || EMPTY_OBJ;
    if (instance) {
        let cur = instance.parent;
        const exposedInstance = instance.proxy;
        const errorInfo = `https://vuejs.org/error-reference/#runtime-${type}`;
        while (cur) {
            const errorCapturedHooks = cur.ec;
            if (errorCapturedHooks) {
                for (let i = 0; i < errorCapturedHooks.length; i++) {
                    if (
                        errorCapturedHooks[i](
                            err,
                            exposedInstance,
                            errorInfo,
                        ) === false
                    ) {
                        return;
                    }
                }
            }
            cur = cur.parent;
        }
        if (errorHandler) {
            pauseTracking();
            callWithErrorHandling(errorHandler, null, 10, [
                err,
                exposedInstance,
                errorInfo,
            ]);
            resetTracking();
            return;
        }
    }
    logError(
        err,
        type,
        contextVNode,
        throwInDev,
        throwUnhandledErrorInProduction,
    );
}
function logError(
    err,
    type,
    contextVNode,
    throwInDev = true,
    throwInProd = false,
) {
    if (throwInProd) {
        throw err;
    } else {
        console.error(err);
    }
}
const queue = [];
let flushIndex = -1;
const pendingPostFlushCbs = [];
let activePostFlushCbs = null;
let postFlushIndex = 0;
const resolvedPromise = /* @__PURE__ */ Promise.resolve();
let currentFlushPromise = null;
function nextTick(fn) {
    const p2 = currentFlushPromise || resolvedPromise;
    return fn ? p2.then(this ? fn.bind(this) : fn) : p2;
}
function findInsertionIndex(id) {
    let start = flushIndex + 1;
    let end = queue.length;
    while (start < end) {
        const middle = (start + end) >>> 1;
        const middleJob = queue[middle];
        const middleJobId = getId(middleJob);
        if (middleJobId < id || (middleJobId === id && middleJob.flags & 2)) {
            start = middle + 1;
        } else {
            end = middle;
        }
    }
    return start;
}
function queueJob(job) {
    if (!(job.flags & 1)) {
        const jobId = getId(job);
        const lastJob = queue[queue.length - 1];
        if (
            !lastJob || // fast path when the job id is larger than the tail
            (!(job.flags & 2) && jobId >= getId(lastJob))
        ) {
            queue.push(job);
        } else {
            queue.splice(findInsertionIndex(jobId), 0, job);
        }
        job.flags |= 1;
        queueFlush();
    }
}
function queueFlush() {
    if (!currentFlushPromise) {
        currentFlushPromise = resolvedPromise.then(flushJobs);
    }
}
function queuePostFlushCb(cb) {
    if (!isArray(cb)) {
        if (activePostFlushCbs && cb.id === -1) {
            activePostFlushCbs.splice(postFlushIndex + 1, 0, cb);
        } else if (!(cb.flags & 1)) {
            pendingPostFlushCbs.push(cb);
            cb.flags |= 1;
        }
    } else {
        pendingPostFlushCbs.push(...cb);
    }
    queueFlush();
}
function flushPreFlushCbs(instance, seen, i = flushIndex + 1) {
    for (; i < queue.length; i++) {
        const cb = queue[i];
        if (cb && cb.flags & 2) {
            if (instance && cb.id !== instance.uid) {
                continue;
            }
            queue.splice(i, 1);
            i--;
            if (cb.flags & 4) {
                cb.flags &= -2;
            }
            cb();
            if (!(cb.flags & 4)) {
                cb.flags &= -2;
            }
        }
    }
}
function flushPostFlushCbs(seen) {
    if (pendingPostFlushCbs.length) {
        const deduped = [...new Set(pendingPostFlushCbs)].sort(
            (a, b) => getId(a) - getId(b),
        );
        pendingPostFlushCbs.length = 0;
        if (activePostFlushCbs) {
            activePostFlushCbs.push(...deduped);
            return;
        }
        activePostFlushCbs = deduped;
        for (
            postFlushIndex = 0;
            postFlushIndex < activePostFlushCbs.length;
            postFlushIndex++
        ) {
            const cb = activePostFlushCbs[postFlushIndex];
            if (cb.flags & 4) {
                cb.flags &= -2;
            }
            if (!(cb.flags & 8)) cb();
            cb.flags &= -2;
        }
        activePostFlushCbs = null;
        postFlushIndex = 0;
    }
}
const getId = (job) =>
    job.id == null ? (job.flags & 2 ? -1 : Infinity) : job.id;
function flushJobs(seen) {
    try {
        for (flushIndex = 0; flushIndex < queue.length; flushIndex++) {
            const job = queue[flushIndex];
            if (job && !(job.flags & 8)) {
                if (false);
                if (job.flags & 4) {
                    job.flags &= ~1;
                }
                callWithErrorHandling(job, job.i, job.i ? 15 : 14);
                if (!(job.flags & 4)) {
                    job.flags &= ~1;
                }
            }
        }
    } finally {
        for (; flushIndex < queue.length; flushIndex++) {
            const job = queue[flushIndex];
            if (job) {
                job.flags &= -2;
            }
        }
        flushIndex = -1;
        queue.length = 0;
        flushPostFlushCbs();
        currentFlushPromise = null;
        if (queue.length || pendingPostFlushCbs.length) {
            flushJobs();
        }
    }
}
let currentRenderingInstance = null;
let currentScopeId = null;
function setCurrentRenderingInstance(instance) {
    const prev = currentRenderingInstance;
    currentRenderingInstance = instance;
    currentScopeId = (instance && instance.type.__scopeId) || null;
    return prev;
}
function withCtx(fn, ctx = currentRenderingInstance, isNonScopedSlot) {
    if (!ctx) return fn;
    if (fn._n) {
        return fn;
    }
    const renderFnWithContext = (...args) => {
        if (renderFnWithContext._d) {
            setBlockTracking(-1);
        }
        const prevInstance = setCurrentRenderingInstance(ctx);
        let res;
        try {
            res = fn(...args);
        } finally {
            setCurrentRenderingInstance(prevInstance);
            if (renderFnWithContext._d) {
                setBlockTracking(1);
            }
        }
        return res;
    };
    renderFnWithContext._n = true;
    renderFnWithContext._c = true;
    renderFnWithContext._d = true;
    return renderFnWithContext;
}
function withDirectives(vnode, directives) {
    if (currentRenderingInstance === null) {
        return vnode;
    }
    const instance = getComponentPublicInstance(currentRenderingInstance);
    const bindings = vnode.dirs || (vnode.dirs = []);
    for (let i = 0; i < directives.length; i++) {
        let [dir, value, arg, modifiers = EMPTY_OBJ] = directives[i];
        if (dir) {
            if (isFunction(dir)) {
                dir = {
                    mounted: dir,
                    updated: dir,
                };
            }
            if (dir.deep) {
                traverse(value);
            }
            bindings.push({
                dir,
                instance,
                value,
                oldValue: void 0,
                arg,
                modifiers,
            });
        }
    }
    return vnode;
}
function invokeDirectiveHook(vnode, prevVNode, instance, name) {
    const bindings = vnode.dirs;
    const oldBindings = prevVNode && prevVNode.dirs;
    for (let i = 0; i < bindings.length; i++) {
        const binding = bindings[i];
        if (oldBindings) {
            binding.oldValue = oldBindings[i].value;
        }
        let hook = binding.dir[name];
        if (hook) {
            pauseTracking();
            callWithAsyncErrorHandling(hook, instance, 8, [
                vnode.el,
                binding,
                vnode,
                prevVNode,
            ]);
            resetTracking();
        }
    }
}
const TeleportEndKey = Symbol("_vte");
const isTeleport = (type) => type.__isTeleport;
function setTransitionHooks(vnode, hooks) {
    if (vnode.shapeFlag & 6 && vnode.component) {
        vnode.transition = hooks;
        setTransitionHooks(vnode.component.subTree, hooks);
    } else if (vnode.shapeFlag & 128) {
        vnode.ssContent.transition = hooks.clone(vnode.ssContent);
        vnode.ssFallback.transition = hooks.clone(vnode.ssFallback);
    } else {
        vnode.transition = hooks;
    }
}
function markAsyncBoundary(instance) {
    instance.ids = [instance.ids[0] + instance.ids[2]++ + "-", 0, 0];
}
function setRef(rawRef, oldRawRef, parentSuspense, vnode, isUnmount = false) {
    if (isArray(rawRef)) {
        rawRef.forEach((r, i) =>
            setRef(
                r,
                oldRawRef && (isArray(oldRawRef) ? oldRawRef[i] : oldRawRef),
                parentSuspense,
                vnode,
                isUnmount,
            ),
        );
        return;
    }
    if (isAsyncWrapper(vnode) && !isUnmount) {
        if (
            vnode.shapeFlag & 512 &&
            vnode.type.__asyncResolved &&
            vnode.component.subTree.component
        ) {
            setRef(rawRef, oldRawRef, parentSuspense, vnode.component.subTree);
        }
        return;
    }
    const refValue =
        vnode.shapeFlag & 4
            ? getComponentPublicInstance(vnode.component)
            : vnode.el;
    const value = isUnmount ? null : refValue;
    const { i: owner, r: ref3 } = rawRef;
    const oldRef = oldRawRef && oldRawRef.r;
    const refs = owner.refs === EMPTY_OBJ ? (owner.refs = {}) : owner.refs;
    const setupState = owner.setupState;
    const rawSetupState = toRaw(setupState);
    const canSetSetupRef =
        setupState === EMPTY_OBJ
            ? () => false
            : (key) => {
                  return hasOwn(rawSetupState, key);
              };
    if (oldRef != null && oldRef !== ref3) {
        if (isString(oldRef)) {
            refs[oldRef] = null;
            if (canSetSetupRef(oldRef)) {
                setupState[oldRef] = null;
            }
        } else if (isRef(oldRef)) {
            oldRef.value = null;
        }
    }
    if (isFunction(ref3)) {
        callWithErrorHandling(ref3, owner, 12, [value, refs]);
    } else {
        const _isString = isString(ref3);
        const _isRef = isRef(ref3);
        if (_isString || _isRef) {
            const doSet = () => {
                if (rawRef.f) {
                    const existing = _isString
                        ? canSetSetupRef(ref3)
                            ? setupState[ref3]
                            : refs[ref3]
                        : ref3.value;
                    if (isUnmount) {
                        isArray(existing) && remove(existing, refValue);
                    } else {
                        if (!isArray(existing)) {
                            if (_isString) {
                                refs[ref3] = [refValue];
                                if (canSetSetupRef(ref3)) {
                                    setupState[ref3] = refs[ref3];
                                }
                            } else {
                                ref3.value = [refValue];
                                if (rawRef.k) refs[rawRef.k] = ref3.value;
                            }
                        } else if (!existing.includes(refValue)) {
                            existing.push(refValue);
                        }
                    }
                } else if (_isString) {
                    refs[ref3] = value;
                    if (canSetSetupRef(ref3)) {
                        setupState[ref3] = value;
                    }
                } else if (_isRef) {
                    ref3.value = value;
                    if (rawRef.k) refs[rawRef.k] = value;
                } else;
            };
            if (value) {
                doSet.id = -1;
                queuePostRenderEffect(doSet, parentSuspense);
            } else {
                doSet();
            }
        }
    }
}
getGlobalThis().requestIdleCallback || ((cb) => setTimeout(cb, 1));
getGlobalThis().cancelIdleCallback || ((id) => clearTimeout(id));
const isAsyncWrapper = (i) => !!i.type.__asyncLoader;
const isKeepAlive = (vnode) => vnode.type.__isKeepAlive;
function onActivated(hook, target) {
    registerKeepAliveHook(hook, "a", target);
}
function onDeactivated(hook, target) {
    registerKeepAliveHook(hook, "da", target);
}
function registerKeepAliveHook(hook, type, target = currentInstance) {
    const wrappedHook =
        hook.__wdc ||
        (hook.__wdc = () => {
            let current = target;
            while (current) {
                if (current.isDeactivated) {
                    return;
                }
                current = current.parent;
            }
            return hook();
        });
    injectHook(type, wrappedHook, target);
    if (target) {
        let current = target.parent;
        while (current && current.parent) {
            if (isKeepAlive(current.parent.vnode)) {
                injectToKeepAliveRoot(wrappedHook, type, target, current);
            }
            current = current.parent;
        }
    }
}
function injectToKeepAliveRoot(hook, type, target, keepAliveRoot) {
    const injected = injectHook(
        type,
        hook,
        keepAliveRoot,
        true,
        /* prepend */
    );
    onUnmounted(() => {
        remove(keepAliveRoot[type], injected);
    }, target);
}
function injectHook(type, hook, target = currentInstance, prepend = false) {
    if (target) {
        const hooks = target[type] || (target[type] = []);
        const wrappedHook =
            hook.__weh ||
            (hook.__weh = (...args) => {
                pauseTracking();
                const reset = setCurrentInstance(target);
                const res = callWithAsyncErrorHandling(
                    hook,
                    target,
                    type,
                    args,
                );
                reset();
                resetTracking();
                return res;
            });
        if (prepend) {
            hooks.unshift(wrappedHook);
        } else {
            hooks.push(wrappedHook);
        }
        return wrappedHook;
    }
}
const createHook =
    (lifecycle) =>
    (hook, target = currentInstance) => {
        if (!isInSSRComponentSetup || lifecycle === "sp") {
            injectHook(lifecycle, (...args) => hook(...args), target);
        }
    };
const onBeforeMount = createHook("bm");
const onMounted = createHook("m");
const onBeforeUpdate = createHook("bu");
const onUpdated = createHook("u");
const onBeforeUnmount = createHook("bum");
const onUnmounted = createHook("um");
const onServerPrefetch = createHook("sp");
const onRenderTriggered = createHook("rtg");
const onRenderTracked = createHook("rtc");
function onErrorCaptured(hook, target = currentInstance) {
    injectHook("ec", hook, target);
}
const NULL_DYNAMIC_COMPONENT = Symbol.for("v-ndc");
function renderList(source, renderItem, cache, index) {
    let ret;
    const cached = cache;
    const sourceIsArray = isArray(source);
    if (sourceIsArray || isString(source)) {
        const sourceIsReactiveArray = sourceIsArray && isReactive(source);
        let needsWrap = false;
        if (sourceIsReactiveArray) {
            needsWrap = !isShallow(source);
            source = shallowReadArray(source);
        }
        ret = new Array(source.length);
        for (let i = 0, l = source.length; i < l; i++) {
            ret[i] = renderItem(
                needsWrap ? toReactive(source[i]) : source[i],
                i,
                void 0,
                cached,
            );
        }
    } else if (typeof source === "number") {
        ret = new Array(source);
        for (let i = 0; i < source; i++) {
            ret[i] = renderItem(i + 1, i, void 0, cached);
        }
    } else if (isObject(source)) {
        if (source[Symbol.iterator]) {
            ret = Array.from(source, (item, i) =>
                renderItem(item, i, void 0, cached),
            );
        } else {
            const keys = Object.keys(source);
            ret = new Array(keys.length);
            for (let i = 0, l = keys.length; i < l; i++) {
                const key = keys[i];
                ret[i] = renderItem(source[key], key, i, cached);
            }
        }
    } else {
        ret = [];
    }
    return ret;
}
const getPublicInstance = (i) => {
    if (!i) return null;
    if (isStatefulComponent(i)) return getComponentPublicInstance(i);
    return getPublicInstance(i.parent);
};
const publicPropertiesMap =
    // Move PURE marker to new line to workaround compiler discarding it
    // due to type annotation
    /* @__PURE__ */ extend(/* @__PURE__ */ Object.create(null), {
        $: (i) => i,
        $el: (i) => i.vnode.el,
        $data: (i) => i.data,
        $props: (i) => i.props,
        $attrs: (i) => i.attrs,
        $slots: (i) => i.slots,
        $refs: (i) => i.refs,
        $parent: (i) => getPublicInstance(i.parent),
        $root: (i) => getPublicInstance(i.root),
        $host: (i) => i.ce,
        $emit: (i) => i.emit,
        $options: (i) => resolveMergedOptions(i),
        $forceUpdate: (i) =>
            i.f ||
            (i.f = () => {
                queueJob(i.update);
            }),
        $nextTick: (i) => i.n || (i.n = nextTick.bind(i.proxy)),
        $watch: (i) => instanceWatch.bind(i),
    });
const hasSetupBinding = (state, key) =>
    state !== EMPTY_OBJ && !state.__isScriptSetup && hasOwn(state, key);
const PublicInstanceProxyHandlers = {
    get({ _: instance }, key) {
        if (key === "__v_skip") {
            return true;
        }
        const { ctx, setupState, data, props, accessCache, type, appContext } =
            instance;
        let normalizedProps;
        if (key[0] !== "$") {
            const n = accessCache[key];
            if (n !== void 0) {
                switch (n) {
                    case 1:
                        return setupState[key];
                    case 2:
                        return data[key];
                    case 4:
                        return ctx[key];
                    case 3:
                        return props[key];
                }
            } else if (hasSetupBinding(setupState, key)) {
                accessCache[key] = 1;
                return setupState[key];
            } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {
                accessCache[key] = 2;
                return data[key];
            } else if (
                // only cache other properties when instance has declared (thus stable)
                // props
                (normalizedProps = instance.propsOptions[0]) &&
                hasOwn(normalizedProps, key)
            ) {
                accessCache[key] = 3;
                return props[key];
            } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {
                accessCache[key] = 4;
                return ctx[key];
            } else if (shouldCacheAccess) {
                accessCache[key] = 0;
            }
        }
        const publicGetter = publicPropertiesMap[key];
        let cssModule, globalProperties;
        if (publicGetter) {
            if (key === "$attrs") {
                track(instance.attrs, "get", "");
            }
            return publicGetter(instance);
        } else if (
            // css module (injected by vue-loader)
            (cssModule = type.__cssModules) &&
            (cssModule = cssModule[key])
        ) {
            return cssModule;
        } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {
            accessCache[key] = 4;
            return ctx[key];
        } else if (
            // global properties
            ((globalProperties = appContext.config.globalProperties),
            hasOwn(globalProperties, key))
        ) {
            {
                return globalProperties[key];
            }
        } else;
    },
    set({ _: instance }, key, value) {
        const { data, setupState, ctx } = instance;
        if (hasSetupBinding(setupState, key)) {
            setupState[key] = value;
            return true;
        } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {
            data[key] = value;
            return true;
        } else if (hasOwn(instance.props, key)) {
            return false;
        }
        if (key[0] === "$" && key.slice(1) in instance) {
            return false;
        } else {
            {
                ctx[key] = value;
            }
        }
        return true;
    },
    has(
        { _: { data, setupState, accessCache, ctx, appContext, propsOptions } },
        key,
    ) {
        let normalizedProps;
        return (
            !!accessCache[key] ||
            (data !== EMPTY_OBJ && hasOwn(data, key)) ||
            hasSetupBinding(setupState, key) ||
            ((normalizedProps = propsOptions[0]) &&
                hasOwn(normalizedProps, key)) ||
            hasOwn(ctx, key) ||
            hasOwn(publicPropertiesMap, key) ||
            hasOwn(appContext.config.globalProperties, key)
        );
    },
    defineProperty(target, key, descriptor) {
        if (descriptor.get != null) {
            target._.accessCache[key] = 0;
        } else if (hasOwn(descriptor, "value")) {
            this.set(target, key, descriptor.value, null);
        }
        return Reflect.defineProperty(target, key, descriptor);
    },
};
function normalizePropsOrEmits(props) {
    return isArray(props)
        ? props.reduce(
              (normalized, p2) => ((normalized[p2] = null), normalized),
              {},
          )
        : props;
}
let shouldCacheAccess = true;
function applyOptions(instance) {
    const options = resolveMergedOptions(instance);
    const publicThis = instance.proxy;
    const ctx = instance.ctx;
    shouldCacheAccess = false;
    if (options.beforeCreate) {
        callHook(options.beforeCreate, instance, "bc");
    }
    const {
        // state
        data: dataOptions,
        computed: computedOptions,
        methods,
        watch: watchOptions,
        provide: provideOptions,
        inject: injectOptions,
        // lifecycle
        created,
        beforeMount,
        mounted,
        beforeUpdate,
        updated,
        activated,
        deactivated,
        beforeDestroy,
        beforeUnmount,
        destroyed,
        unmounted,
        render,
        renderTracked,
        renderTriggered,
        errorCaptured,
        serverPrefetch,
        // public API
        expose,
        inheritAttrs,
        // assets
        components,
        directives,
        filters,
    } = options;
    const checkDuplicateProperties = null;
    if (injectOptions) {
        resolveInjections(injectOptions, ctx, checkDuplicateProperties);
    }
    if (methods) {
        for (const key in methods) {
            const methodHandler = methods[key];
            if (isFunction(methodHandler)) {
                {
                    ctx[key] = methodHandler.bind(publicThis);
                }
            }
        }
    }
    if (dataOptions) {
        const data = dataOptions.call(publicThis, publicThis);
        if (!isObject(data));
        else {
            instance.data = reactive(data);
        }
    }
    shouldCacheAccess = true;
    if (computedOptions) {
        for (const key in computedOptions) {
            const opt = computedOptions[key];
            const get2 = isFunction(opt)
                ? opt.bind(publicThis, publicThis)
                : isFunction(opt.get)
                  ? opt.get.bind(publicThis, publicThis)
                  : NOOP;
            const set2 =
                !isFunction(opt) && isFunction(opt.set)
                    ? opt.set.bind(publicThis)
                    : NOOP;
            const c = computed({
                get: get2,
                set: set2,
            });
            Object.defineProperty(ctx, key, {
                enumerable: true,
                configurable: true,
                get: () => c.value,
                set: (v) => (c.value = v),
            });
        }
    }
    if (watchOptions) {
        for (const key in watchOptions) {
            createWatcher(watchOptions[key], ctx, publicThis, key);
        }
    }
    if (provideOptions) {
        const provides = isFunction(provideOptions)
            ? provideOptions.call(publicThis)
            : provideOptions;
        Reflect.ownKeys(provides).forEach((key) => {
            provide(key, provides[key]);
        });
    }
    if (created) {
        callHook(created, instance, "c");
    }
    function registerLifecycleHook(register, hook) {
        if (isArray(hook)) {
            hook.forEach((_hook) => register(_hook.bind(publicThis)));
        } else if (hook) {
            register(hook.bind(publicThis));
        }
    }
    registerLifecycleHook(onBeforeMount, beforeMount);
    registerLifecycleHook(onMounted, mounted);
    registerLifecycleHook(onBeforeUpdate, beforeUpdate);
    registerLifecycleHook(onUpdated, updated);
    registerLifecycleHook(onActivated, activated);
    registerLifecycleHook(onDeactivated, deactivated);
    registerLifecycleHook(onErrorCaptured, errorCaptured);
    registerLifecycleHook(onRenderTracked, renderTracked);
    registerLifecycleHook(onRenderTriggered, renderTriggered);
    registerLifecycleHook(onBeforeUnmount, beforeUnmount);
    registerLifecycleHook(onUnmounted, unmounted);
    registerLifecycleHook(onServerPrefetch, serverPrefetch);
    if (isArray(expose)) {
        if (expose.length) {
            const exposed = instance.exposed || (instance.exposed = {});
            expose.forEach((key) => {
                Object.defineProperty(exposed, key, {
                    get: () => publicThis[key],
                    set: (val) => (publicThis[key] = val),
                });
            });
        } else if (!instance.exposed) {
            instance.exposed = {};
        }
    }
    if (render && instance.render === NOOP) {
        instance.render = render;
    }
    if (inheritAttrs != null) {
        instance.inheritAttrs = inheritAttrs;
    }
    if (components) instance.components = components;
    if (directives) instance.directives = directives;
    if (serverPrefetch) {
        markAsyncBoundary(instance);
    }
}
function resolveInjections(
    injectOptions,
    ctx,
    checkDuplicateProperties = NOOP,
) {
    if (isArray(injectOptions)) {
        injectOptions = normalizeInject(injectOptions);
    }
    for (const key in injectOptions) {
        const opt = injectOptions[key];
        let injected;
        if (isObject(opt)) {
            if ("default" in opt) {
                injected = inject(opt.from || key, opt.default, true);
            } else {
                injected = inject(opt.from || key);
            }
        } else {
            injected = inject(opt);
        }
        if (isRef(injected)) {
            Object.defineProperty(ctx, key, {
                enumerable: true,
                configurable: true,
                get: () => injected.value,
                set: (v) => (injected.value = v),
            });
        } else {
            ctx[key] = injected;
        }
    }
}
function callHook(hook, instance, type) {
    callWithAsyncErrorHandling(
        isArray(hook)
            ? hook.map((h2) => h2.bind(instance.proxy))
            : hook.bind(instance.proxy),
        instance,
        type,
    );
}
function createWatcher(raw, ctx, publicThis, key) {
    let getter = key.includes(".")
        ? createPathGetter(publicThis, key)
        : () => publicThis[key];
    if (isString(raw)) {
        const handler = ctx[raw];
        if (isFunction(handler)) {
            {
                watch(getter, handler);
            }
        }
    } else if (isFunction(raw)) {
        {
            watch(getter, raw.bind(publicThis));
        }
    } else if (isObject(raw)) {
        if (isArray(raw)) {
            raw.forEach((r) => createWatcher(r, ctx, publicThis, key));
        } else {
            const handler = isFunction(raw.handler)
                ? raw.handler.bind(publicThis)
                : ctx[raw.handler];
            if (isFunction(handler)) {
                watch(getter, handler, raw);
            }
        }
    } else;
}
function resolveMergedOptions(instance) {
    const base = instance.type;
    const { mixins, extends: extendsOptions } = base;
    const {
        mixins: globalMixins,
        optionsCache: cache,
        config: { optionMergeStrategies },
    } = instance.appContext;
    const cached = cache.get(base);
    let resolved;
    if (cached) {
        resolved = cached;
    } else if (!globalMixins.length && !mixins && !extendsOptions) {
        {
            resolved = base;
        }
    } else {
        resolved = {};
        if (globalMixins.length) {
            globalMixins.forEach((m) =>
                mergeOptions(resolved, m, optionMergeStrategies, true),
            );
        }
        mergeOptions(resolved, base, optionMergeStrategies);
    }
    if (isObject(base)) {
        cache.set(base, resolved);
    }
    return resolved;
}
function mergeOptions(to, from, strats, asMixin = false) {
    const { mixins, extends: extendsOptions } = from;
    if (extendsOptions) {
        mergeOptions(to, extendsOptions, strats, true);
    }
    if (mixins) {
        mixins.forEach((m) => mergeOptions(to, m, strats, true));
    }
    for (const key in from) {
        if (asMixin && key === "expose");
        else {
            const strat =
                internalOptionMergeStrats[key] || (strats && strats[key]);
            to[key] = strat ? strat(to[key], from[key]) : from[key];
        }
    }
    return to;
}
const internalOptionMergeStrats = {
    data: mergeDataFn,
    props: mergeEmitsOrPropsOptions,
    emits: mergeEmitsOrPropsOptions,
    // objects
    methods: mergeObjectOptions,
    computed: mergeObjectOptions,
    // lifecycle
    beforeCreate: mergeAsArray,
    created: mergeAsArray,
    beforeMount: mergeAsArray,
    mounted: mergeAsArray,
    beforeUpdate: mergeAsArray,
    updated: mergeAsArray,
    beforeDestroy: mergeAsArray,
    beforeUnmount: mergeAsArray,
    destroyed: mergeAsArray,
    unmounted: mergeAsArray,
    activated: mergeAsArray,
    deactivated: mergeAsArray,
    errorCaptured: mergeAsArray,
    serverPrefetch: mergeAsArray,
    // assets
    components: mergeObjectOptions,
    directives: mergeObjectOptions,
    // watch
    watch: mergeWatchOptions,
    // provide / inject
    provide: mergeDataFn,
    inject: mergeInject,
};
function mergeDataFn(to, from) {
    if (!from) {
        return to;
    }
    if (!to) {
        return from;
    }
    return function mergedDataFn() {
        return extend(
            isFunction(to) ? to.call(this, this) : to,
            isFunction(from) ? from.call(this, this) : from,
        );
    };
}
function mergeInject(to, from) {
    return mergeObjectOptions(normalizeInject(to), normalizeInject(from));
}
function normalizeInject(raw) {
    if (isArray(raw)) {
        const res = {};
        for (let i = 0; i < raw.length; i++) {
            res[raw[i]] = raw[i];
        }
        return res;
    }
    return raw;
}
function mergeAsArray(to, from) {
    return to ? [...new Set([].concat(to, from))] : from;
}
function mergeObjectOptions(to, from) {
    return to ? extend(/* @__PURE__ */ Object.create(null), to, from) : from;
}
function mergeEmitsOrPropsOptions(to, from) {
    if (to) {
        if (isArray(to) && isArray(from)) {
            return [.../* @__PURE__ */ new Set([...to, ...from])];
        }
        return extend(
            /* @__PURE__ */ Object.create(null),
            normalizePropsOrEmits(to),
            normalizePropsOrEmits(from != null ? from : {}),
        );
    } else {
        return from;
    }
}
function mergeWatchOptions(to, from) {
    if (!to) return from;
    if (!from) return to;
    const merged = extend(/* @__PURE__ */ Object.create(null), to);
    for (const key in from) {
        merged[key] = mergeAsArray(to[key], from[key]);
    }
    return merged;
}
function createAppContext() {
    return {
        app: null,
        config: {
            isNativeTag: NO,
            performance: false,
            globalProperties: {},
            optionMergeStrategies: {},
            errorHandler: void 0,
            warnHandler: void 0,
            compilerOptions: {},
        },
        mixins: [],
        components: {},
        directives: {},
        provides: /* @__PURE__ */ Object.create(null),
        optionsCache: /* @__PURE__ */ new WeakMap(),
        propsCache: /* @__PURE__ */ new WeakMap(),
        emitsCache: /* @__PURE__ */ new WeakMap(),
    };
}
let uid$1 = 0;
function createAppAPI(render, hydrate) {
    return function createApp2(rootComponent, rootProps = null) {
        if (!isFunction(rootComponent)) {
            rootComponent = extend({}, rootComponent);
        }
        if (rootProps != null && !isObject(rootProps)) {
            rootProps = null;
        }
        const context = createAppContext();
        const installedPlugins = /* @__PURE__ */ new WeakSet();
        const pluginCleanupFns = [];
        let isMounted = false;
        const app = (context.app = {
            _uid: uid$1++,
            _component: rootComponent,
            _props: rootProps,
            _container: null,
            _context: context,
            _instance: null,
            version: version$1,
            get config() {
                return context.config;
            },
            set config(v) {},
            use(plugin, ...options) {
                if (installedPlugins.has(plugin));
                else if (plugin && isFunction(plugin.install)) {
                    installedPlugins.add(plugin);
                    plugin.install(app, ...options);
                } else if (isFunction(plugin)) {
                    installedPlugins.add(plugin);
                    plugin(app, ...options);
                } else;
                return app;
            },
            mixin(mixin) {
                {
                    if (!context.mixins.includes(mixin)) {
                        context.mixins.push(mixin);
                    }
                }
                return app;
            },
            component(name, component) {
                if (!component) {
                    return context.components[name];
                }
                context.components[name] = component;
                return app;
            },
            directive(name, directive) {
                if (!directive) {
                    return context.directives[name];
                }
                context.directives[name] = directive;
                return app;
            },
            mount(rootContainer, isHydrate, namespace) {
                if (!isMounted) {
                    const vnode =
                        app._ceVNode || createVNode(rootComponent, rootProps);
                    vnode.appContext = context;
                    if (namespace === true) {
                        namespace = "svg";
                    } else if (namespace === false) {
                        namespace = void 0;
                    }
                    {
                        render(vnode, rootContainer, namespace);
                    }
                    isMounted = true;
                    app._container = rootContainer;
                    rootContainer.__vue_app__ = app;
                    return getComponentPublicInstance(vnode.component);
                }
            },
            onUnmount(cleanupFn) {
                pluginCleanupFns.push(cleanupFn);
            },
            unmount() {
                if (isMounted) {
                    callWithAsyncErrorHandling(
                        pluginCleanupFns,
                        app._instance,
                        16,
                    );
                    render(null, app._container);
                    delete app._container.__vue_app__;
                }
            },
            provide(key, value) {
                context.provides[key] = value;
                return app;
            },
            runWithContext(fn) {
                const lastApp = currentApp;
                currentApp = app;
                try {
                    return fn();
                } finally {
                    currentApp = lastApp;
                }
            },
        });
        return app;
    };
}
let currentApp = null;
function provide(key, value) {
    if (!currentInstance);
    else {
        let provides = currentInstance.provides;
        const parentProvides =
            currentInstance.parent && currentInstance.parent.provides;
        if (parentProvides === provides) {
            provides = currentInstance.provides = Object.create(parentProvides);
        }
        provides[key] = value;
    }
}
function inject(key, defaultValue, treatDefaultAsFactory = false) {
    const instance = currentInstance || currentRenderingInstance;
    if (instance || currentApp) {
        const provides = currentApp
            ? currentApp._context.provides
            : instance
              ? instance.parent == null
                  ? instance.vnode.appContext &&
                    instance.vnode.appContext.provides
                  : instance.parent.provides
              : void 0;
        if (provides && key in provides) {
            return provides[key];
        } else if (arguments.length > 1) {
            return treatDefaultAsFactory && isFunction(defaultValue)
                ? defaultValue.call(instance && instance.proxy)
                : defaultValue;
        } else;
    }
}
function hasInjectionContext() {
    return !!(currentInstance || currentRenderingInstance || currentApp);
}
const internalObjectProto = {};
const createInternalObject = () => Object.create(internalObjectProto);
const isInternalObject = (obj) =>
    Object.getPrototypeOf(obj) === internalObjectProto;
function initProps(instance, rawProps, isStateful, isSSR = false) {
    const props = {};
    const attrs = createInternalObject();
    instance.propsDefaults = /* @__PURE__ */ Object.create(null);
    setFullProps(instance, rawProps, props, attrs);
    for (const key in instance.propsOptions[0]) {
        if (!(key in props)) {
            props[key] = void 0;
        }
    }
    if (isStateful) {
        instance.props = isSSR ? props : shallowReactive(props);
    } else {
        if (!instance.type.props) {
            instance.props = attrs;
        } else {
            instance.props = props;
        }
    }
    instance.attrs = attrs;
}
function updateProps(instance, rawProps, rawPrevProps, optimized) {
    const {
        props,
        attrs,
        vnode: { patchFlag },
    } = instance;
    const rawCurrentProps = toRaw(props);
    const [options] = instance.propsOptions;
    let hasAttrsChanged = false;
    if (
        // always force full diff in dev
        // - #1942 if hmr is enabled with sfc component
        // - vite#872 non-sfc component used by sfc component
        (optimized || patchFlag > 0) &&
        !(patchFlag & 16)
    ) {
        if (patchFlag & 8) {
            const propsToUpdate = instance.vnode.dynamicProps;
            for (let i = 0; i < propsToUpdate.length; i++) {
                let key = propsToUpdate[i];
                if (isEmitListener(instance.emitsOptions, key)) {
                    continue;
                }
                const value = rawProps[key];
                if (options) {
                    if (hasOwn(attrs, key)) {
                        if (value !== attrs[key]) {
                            attrs[key] = value;
                            hasAttrsChanged = true;
                        }
                    } else {
                        const camelizedKey = camelize(key);
                        props[camelizedKey] = resolvePropValue(
                            options,
                            rawCurrentProps,
                            camelizedKey,
                            value,
                            instance,
                            false,
                        );
                    }
                } else {
                    if (value !== attrs[key]) {
                        attrs[key] = value;
                        hasAttrsChanged = true;
                    }
                }
            }
        }
    } else {
        if (setFullProps(instance, rawProps, props, attrs)) {
            hasAttrsChanged = true;
        }
        let kebabKey;
        for (const key in rawCurrentProps) {
            if (
                !rawProps || // for camelCase
                (!hasOwn(rawProps, key) && // it's possible the original props was passed in as kebab-case
                    // and converted to camelCase (#955)
                    ((kebabKey = hyphenate(key)) === key ||
                        !hasOwn(rawProps, kebabKey)))
            ) {
                if (options) {
                    if (
                        rawPrevProps && // for camelCase
                        (rawPrevProps[key] !== void 0 || // for kebab-case
                            rawPrevProps[kebabKey] !== void 0)
                    ) {
                        props[key] = resolvePropValue(
                            options,
                            rawCurrentProps,
                            key,
                            void 0,
                            instance,
                            true,
                        );
                    }
                } else {
                    delete props[key];
                }
            }
        }
        if (attrs !== rawCurrentProps) {
            for (const key in attrs) {
                if (!rawProps || (!hasOwn(rawProps, key) && true)) {
                    delete attrs[key];
                    hasAttrsChanged = true;
                }
            }
        }
    }
    if (hasAttrsChanged) {
        trigger(instance.attrs, "set", "");
    }
}
function setFullProps(instance, rawProps, props, attrs) {
    const [options, needCastKeys] = instance.propsOptions;
    let hasAttrsChanged = false;
    let rawCastValues;
    if (rawProps) {
        for (let key in rawProps) {
            if (isReservedProp(key)) {
                continue;
            }
            const value = rawProps[key];
            let camelKey;
            if (options && hasOwn(options, (camelKey = camelize(key)))) {
                if (!needCastKeys || !needCastKeys.includes(camelKey)) {
                    props[camelKey] = value;
                } else {
                    (rawCastValues || (rawCastValues = {}))[camelKey] = value;
                }
            } else if (!isEmitListener(instance.emitsOptions, key)) {
                if (!(key in attrs) || value !== attrs[key]) {
                    attrs[key] = value;
                    hasAttrsChanged = true;
                }
            }
        }
    }
    if (needCastKeys) {
        const rawCurrentProps = toRaw(props);
        const castValues = rawCastValues || EMPTY_OBJ;
        for (let i = 0; i < needCastKeys.length; i++) {
            const key = needCastKeys[i];
            props[key] = resolvePropValue(
                options,
                rawCurrentProps,
                key,
                castValues[key],
                instance,
                !hasOwn(castValues, key),
            );
        }
    }
    return hasAttrsChanged;
}
function resolvePropValue(options, props, key, value, instance, isAbsent) {
    const opt = options[key];
    if (opt != null) {
        const hasDefault = hasOwn(opt, "default");
        if (hasDefault && value === void 0) {
            const defaultValue = opt.default;
            if (
                opt.type !== Function &&
                !opt.skipFactory &&
                isFunction(defaultValue)
            ) {
                const { propsDefaults } = instance;
                if (key in propsDefaults) {
                    value = propsDefaults[key];
                } else {
                    const reset = setCurrentInstance(instance);
                    value = propsDefaults[key] = defaultValue.call(null, props);
                    reset();
                }
            } else {
                value = defaultValue;
            }
            if (instance.ce) {
                instance.ce._setProp(key, value);
            }
        }
        if (
            opt[0]
            /* shouldCast */
        ) {
            if (isAbsent && !hasDefault) {
                value = false;
            } else if (
                opt[1] &&
                /* shouldCastTrue */
                (value === "" || value === hyphenate(key))
            ) {
                value = true;
            }
        }
    }
    return value;
}
const mixinPropsCache = /* @__PURE__ */ new WeakMap();
function normalizePropsOptions(comp, appContext, asMixin = false) {
    const cache = asMixin ? mixinPropsCache : appContext.propsCache;
    const cached = cache.get(comp);
    if (cached) {
        return cached;
    }
    const raw = comp.props;
    const normalized = {};
    const needCastKeys = [];
    let hasExtends = false;
    if (!isFunction(comp)) {
        const extendProps = (raw2) => {
            hasExtends = true;
            const [props, keys] = normalizePropsOptions(raw2, appContext, true);
            extend(normalized, props);
            if (keys) needCastKeys.push(...keys);
        };
        if (!asMixin && appContext.mixins.length) {
            appContext.mixins.forEach(extendProps);
        }
        if (comp.extends) {
            extendProps(comp.extends);
        }
        if (comp.mixins) {
            comp.mixins.forEach(extendProps);
        }
    }
    if (!raw && !hasExtends) {
        if (isObject(comp)) {
            cache.set(comp, EMPTY_ARR);
        }
        return EMPTY_ARR;
    }
    if (isArray(raw)) {
        for (let i = 0; i < raw.length; i++) {
            const normalizedKey = camelize(raw[i]);
            if (validatePropName(normalizedKey)) {
                normalized[normalizedKey] = EMPTY_OBJ;
            }
        }
    } else if (raw) {
        for (const key in raw) {
            const normalizedKey = camelize(key);
            if (validatePropName(normalizedKey)) {
                const opt = raw[key];
                const prop = (normalized[normalizedKey] =
                    isArray(opt) || isFunction(opt)
                        ? { type: opt }
                        : extend({}, opt));
                const propType = prop.type;
                let shouldCast = false;
                let shouldCastTrue = true;
                if (isArray(propType)) {
                    for (let index = 0; index < propType.length; ++index) {
                        const type = propType[index];
                        const typeName = isFunction(type) && type.name;
                        if (typeName === "Boolean") {
                            shouldCast = true;
                            break;
                        } else if (typeName === "String") {
                            shouldCastTrue = false;
                        }
                    }
                } else {
                    shouldCast =
                        isFunction(propType) && propType.name === "Boolean";
                }
                prop[0] =
                /* shouldCast */
                    shouldCast;
                prop[1] =
                /* shouldCastTrue */
                    shouldCastTrue;
                if (shouldCast || hasOwn(prop, "default")) {
                    needCastKeys.push(normalizedKey);
                }
            }
        }
    }
    const res = [normalized, needCastKeys];
    if (isObject(comp)) {
        cache.set(comp, res);
    }
    return res;
}
function validatePropName(key) {
    if (key[0] !== "$" && !isReservedProp(key)) {
        return true;
    }
    return false;
}
const isInternalKey = (key) => key[0] === "_" || key === "$stable";
const normalizeSlotValue = (value) =>
    isArray(value) ? value.map(normalizeVNode) : [normalizeVNode(value)];
const normalizeSlot = (key, rawSlot, ctx) => {
    if (rawSlot._n) {
        return rawSlot;
    }
    const normalized = withCtx((...args) => {
        if (false);
        return normalizeSlotValue(rawSlot(...args));
    }, ctx);
    normalized._c = false;
    return normalized;
};
const normalizeObjectSlots = (rawSlots, slots, instance) => {
    const ctx = rawSlots._ctx;
    for (const key in rawSlots) {
        if (isInternalKey(key)) continue;
        const value = rawSlots[key];
        if (isFunction(value)) {
            slots[key] = normalizeSlot(key, value, ctx);
        } else if (value != null) {
            const normalized = normalizeSlotValue(value);
            slots[key] = () => normalized;
        }
    }
};
const normalizeVNodeSlots = (instance, children) => {
    const normalized = normalizeSlotValue(children);
    instance.slots.default = () => normalized;
};
const assignSlots = (slots, children, optimized) => {
    for (const key in children) {
        if (optimized || key !== "_") {
            slots[key] = children[key];
        }
    }
};
const initSlots = (instance, children, optimized) => {
    const slots = (instance.slots = createInternalObject());
    if (instance.vnode.shapeFlag & 32) {
        const type = children._;
        if (type) {
            assignSlots(slots, children, optimized);
            if (optimized) {
                def(slots, "_", type, true);
            }
        } else {
            normalizeObjectSlots(children, slots);
        }
    } else if (children) {
        normalizeVNodeSlots(instance, children);
    }
};
const updateSlots = (instance, children, optimized) => {
    const { vnode, slots } = instance;
    let needDeletionCheck = true;
    let deletionComparisonTarget = EMPTY_OBJ;
    if (vnode.shapeFlag & 32) {
        const type = children._;
        if (type) {
            if (optimized && type === 1) {
                needDeletionCheck = false;
            } else {
                assignSlots(slots, children, optimized);
            }
        } else {
            needDeletionCheck = !children.$stable;
            normalizeObjectSlots(children, slots);
        }
        deletionComparisonTarget = children;
    } else if (children) {
        normalizeVNodeSlots(instance, children);
        deletionComparisonTarget = { default: 1 };
    }
    if (needDeletionCheck) {
        for (const key in slots) {
            if (!isInternalKey(key) && deletionComparisonTarget[key] == null) {
                delete slots[key];
            }
        }
    }
};
const queuePostRenderEffect = queueEffectWithSuspense;
function createRenderer(options) {
    return baseCreateRenderer(options);
}
function baseCreateRenderer(options, createHydrationFns) {
    const target = getGlobalThis();
    target.__VUE__ = true;
    const {
        insert: hostInsert,
        remove: hostRemove,
        patchProp: hostPatchProp,
        createElement: hostCreateElement,
        createText: hostCreateText,
        createComment: hostCreateComment,
        setText: hostSetText,
        setElementText: hostSetElementText,
        parentNode: hostParentNode,
        nextSibling: hostNextSibling,
        setScopeId: hostSetScopeId = NOOP,
        insertStaticContent: hostInsertStaticContent,
    } = options;
    const patch = (
        n1,
        n2,
        container,
        anchor = null,
        parentComponent = null,
        parentSuspense = null,
        namespace = void 0,
        slotScopeIds = null,
        optimized = !!n2.dynamicChildren,
    ) => {
        if (n1 === n2) {
            return;
        }
        if (n1 && !isSameVNodeType(n1, n2)) {
            anchor = getNextHostNode(n1);
            unmount(n1, parentComponent, parentSuspense, true);
            n1 = null;
        }
        if (n2.patchFlag === -2) {
            optimized = false;
            n2.dynamicChildren = null;
        }
        const { type, ref: ref3, shapeFlag } = n2;
        switch (type) {
            case Text:
                processText(n1, n2, container, anchor);
                break;
            case Comment:
                processCommentNode(n1, n2, container, anchor);
                break;
            case Static:
                if (n1 == null) {
                    mountStaticNode(n2, container, anchor, namespace);
                }
                break;
            case Fragment:
                processFragment(
                    n1,
                    n2,
                    container,
                    anchor,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    slotScopeIds,
                    optimized,
                );
                break;
            default:
                if (shapeFlag & 1) {
                    processElement(
                        n1,
                        n2,
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                    );
                } else if (shapeFlag & 6) {
                    processComponent(
                        n1,
                        n2,
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                    );
                } else if (shapeFlag & 64) {
                    type.process(
                        n1,
                        n2,
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                        internals,
                    );
                } else if (shapeFlag & 128) {
                    type.process(
                        n1,
                        n2,
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                        internals,
                    );
                } else;
        }
        if (ref3 != null && parentComponent) {
            setRef(ref3, n1 && n1.ref, parentSuspense, n2 || n1, !n2);
        }
    };
    const processText = (n1, n2, container, anchor) => {
        if (n1 == null) {
            hostInsert(
                (n2.el = hostCreateText(n2.children)),
                container,
                anchor,
            );
        } else {
            const el = (n2.el = n1.el);
            if (n2.children !== n1.children) {
                hostSetText(el, n2.children);
            }
        }
    };
    const processCommentNode = (n1, n2, container, anchor) => {
        if (n1 == null) {
            hostInsert(
                (n2.el = hostCreateComment(n2.children || "")),
                container,
                anchor,
            );
        } else {
            n2.el = n1.el;
        }
    };
    const mountStaticNode = (n2, container, anchor, namespace) => {
        [n2.el, n2.anchor] = hostInsertStaticContent(
            n2.children,
            container,
            anchor,
            namespace,
            n2.el,
            n2.anchor,
        );
    };
    const moveStaticNode = ({ el, anchor }, container, nextSibling) => {
        let next;
        while (el && el !== anchor) {
            next = hostNextSibling(el);
            hostInsert(el, container, nextSibling);
            el = next;
        }
        hostInsert(anchor, container, nextSibling);
    };
    const removeStaticNode = ({ el, anchor }) => {
        let next;
        while (el && el !== anchor) {
            next = hostNextSibling(el);
            hostRemove(el);
            el = next;
        }
        hostRemove(anchor);
    };
    const processElement = (
        n1,
        n2,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
    ) => {
        if (n2.type === "svg") {
            namespace = "svg";
        } else if (n2.type === "math") {
            namespace = "mathml";
        }
        if (n1 == null) {
            mountElement(
                n2,
                container,
                anchor,
                parentComponent,
                parentSuspense,
                namespace,
                slotScopeIds,
                optimized,
            );
        } else {
            patchElement(
                n1,
                n2,
                parentComponent,
                parentSuspense,
                namespace,
                slotScopeIds,
                optimized,
            );
        }
    };
    const mountElement = (
        vnode,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
    ) => {
        let el;
        let vnodeHook;
        const { props, shapeFlag, transition, dirs } = vnode;
        el = vnode.el = hostCreateElement(
            vnode.type,
            namespace,
            props && props.is,
            props,
        );
        if (shapeFlag & 8) {
            hostSetElementText(el, vnode.children);
        } else if (shapeFlag & 16) {
            mountChildren(
                vnode.children,
                el,
                null,
                parentComponent,
                parentSuspense,
                resolveChildrenNamespace(vnode, namespace),
                slotScopeIds,
                optimized,
            );
        }
        if (dirs) {
            invokeDirectiveHook(vnode, null, parentComponent, "created");
        }
        setScopeId(el, vnode, vnode.scopeId, slotScopeIds, parentComponent);
        if (props) {
            for (const key in props) {
                if (key !== "value" && !isReservedProp(key)) {
                    hostPatchProp(
                        el,
                        key,
                        null,
                        props[key],
                        namespace,
                        parentComponent,
                    );
                }
            }
            if ("value" in props) {
                hostPatchProp(el, "value", null, props.value, namespace);
            }
            if ((vnodeHook = props.onVnodeBeforeMount)) {
                invokeVNodeHook(vnodeHook, parentComponent, vnode);
            }
        }
        if (dirs) {
            invokeDirectiveHook(vnode, null, parentComponent, "beforeMount");
        }
        const needCallTransitionHooks = needTransition(
            parentSuspense,
            transition,
        );
        if (needCallTransitionHooks) {
            transition.beforeEnter(el);
        }
        hostInsert(el, container, anchor);
        if (
            (vnodeHook = props && props.onVnodeMounted) ||
            needCallTransitionHooks ||
            dirs
        ) {
            queuePostRenderEffect(() => {
                vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);
                needCallTransitionHooks && transition.enter(el);
                dirs &&
                    invokeDirectiveHook(
                        vnode,
                        null,
                        parentComponent,
                        "mounted",
                    );
            }, parentSuspense);
        }
    };
    const setScopeId = (el, vnode, scopeId, slotScopeIds, parentComponent) => {
        if (scopeId) {
            hostSetScopeId(el, scopeId);
        }
        if (slotScopeIds) {
            for (let i = 0; i < slotScopeIds.length; i++) {
                hostSetScopeId(el, slotScopeIds[i]);
            }
        }
        if (parentComponent) {
            let subTree = parentComponent.subTree;
            if (
                vnode === subTree ||
                (isSuspense(subTree.type) &&
                    (subTree.ssContent === vnode ||
                        subTree.ssFallback === vnode))
            ) {
                const parentVNode = parentComponent.vnode;
                setScopeId(
                    el,
                    parentVNode,
                    parentVNode.scopeId,
                    parentVNode.slotScopeIds,
                    parentComponent.parent,
                );
            }
        }
    };
    const mountChildren = (
        children,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
        start = 0,
    ) => {
        for (let i = start; i < children.length; i++) {
            const child = (children[i] = optimized
                ? cloneIfMounted(children[i])
                : normalizeVNode(children[i]));
            patch(
                null,
                child,
                container,
                anchor,
                parentComponent,
                parentSuspense,
                namespace,
                slotScopeIds,
                optimized,
            );
        }
    };
    const patchElement = (
        n1,
        n2,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
    ) => {
        const el = (n2.el = n1.el);
        let { patchFlag, dynamicChildren, dirs } = n2;
        patchFlag |= n1.patchFlag & 16;
        const oldProps = n1.props || EMPTY_OBJ;
        const newProps = n2.props || EMPTY_OBJ;
        let vnodeHook;
        parentComponent && toggleRecurse(parentComponent, false);
        if ((vnodeHook = newProps.onVnodeBeforeUpdate)) {
            invokeVNodeHook(vnodeHook, parentComponent, n2, n1);
        }
        if (dirs) {
            invokeDirectiveHook(n2, n1, parentComponent, "beforeUpdate");
        }
        parentComponent && toggleRecurse(parentComponent, true);
        if (
            (oldProps.innerHTML && newProps.innerHTML == null) ||
            (oldProps.textContent && newProps.textContent == null)
        ) {
            hostSetElementText(el, "");
        }
        if (dynamicChildren) {
            patchBlockChildren(
                n1.dynamicChildren,
                dynamicChildren,
                el,
                parentComponent,
                parentSuspense,
                resolveChildrenNamespace(n2, namespace),
                slotScopeIds,
            );
        } else if (!optimized) {
            patchChildren(
                n1,
                n2,
                el,
                null,
                parentComponent,
                parentSuspense,
                resolveChildrenNamespace(n2, namespace),
                slotScopeIds,
                false,
            );
        }
        if (patchFlag > 0) {
            if (patchFlag & 16) {
                patchProps(el, oldProps, newProps, parentComponent, namespace);
            } else {
                if (patchFlag & 2) {
                    if (oldProps.class !== newProps.class) {
                        hostPatchProp(
                            el,
                            "class",
                            null,
                            newProps.class,
                            namespace,
                        );
                    }
                }
                if (patchFlag & 4) {
                    hostPatchProp(
                        el,
                        "style",
                        oldProps.style,
                        newProps.style,
                        namespace,
                    );
                }
                if (patchFlag & 8) {
                    const propsToUpdate = n2.dynamicProps;
                    for (let i = 0; i < propsToUpdate.length; i++) {
                        const key = propsToUpdate[i];
                        const prev = oldProps[key];
                        const next = newProps[key];
                        if (next !== prev || key === "value") {
                            hostPatchProp(
                                el,
                                key,
                                prev,
                                next,
                                namespace,
                                parentComponent,
                            );
                        }
                    }
                }
            }
            if (patchFlag & 1) {
                if (n1.children !== n2.children) {
                    hostSetElementText(el, n2.children);
                }
            }
        } else if (!optimized && dynamicChildren == null) {
            patchProps(el, oldProps, newProps, parentComponent, namespace);
        }
        if ((vnodeHook = newProps.onVnodeUpdated) || dirs) {
            queuePostRenderEffect(() => {
                vnodeHook &&
                    invokeVNodeHook(vnodeHook, parentComponent, n2, n1);
                dirs && invokeDirectiveHook(n2, n1, parentComponent, "updated");
            }, parentSuspense);
        }
    };
    const patchBlockChildren = (
        oldChildren,
        newChildren,
        fallbackContainer,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
    ) => {
        for (let i = 0; i < newChildren.length; i++) {
            const oldVNode = oldChildren[i];
            const newVNode = newChildren[i];
            const container =
                // oldVNode may be an errored async setup() component inside Suspense
                // which will not have a mounted element
                oldVNode.el && // - In the case of a Fragment, we need to provide the actual parent
                // of the Fragment itself so it can move its children.
                (oldVNode.type === Fragment || // - In the case of different nodes, there is going to be a replacement
                    // which also requires the correct parent container
                    !isSameVNodeType(oldVNode, newVNode) || // - In the case of a component, it could contain anything.
                    oldVNode.shapeFlag & (6 | 64))
                    ? hostParentNode(oldVNode.el)
                    : // In other cases, the parent container is not actually used so we
                      // just pass the block element here to avoid a DOM parentNode call.
                      fallbackContainer;
            patch(
                oldVNode,
                newVNode,
                container,
                null,
                parentComponent,
                parentSuspense,
                namespace,
                slotScopeIds,
                true,
            );
        }
    };
    const patchProps = (el, oldProps, newProps, parentComponent, namespace) => {
        if (oldProps !== newProps) {
            if (oldProps !== EMPTY_OBJ) {
                for (const key in oldProps) {
                    if (!isReservedProp(key) && !(key in newProps)) {
                        hostPatchProp(
                            el,
                            key,
                            oldProps[key],
                            null,
                            namespace,
                            parentComponent,
                        );
                    }
                }
            }
            for (const key in newProps) {
                if (isReservedProp(key)) continue;
                const next = newProps[key];
                const prev = oldProps[key];
                if (next !== prev && key !== "value") {
                    hostPatchProp(
                        el,
                        key,
                        prev,
                        next,
                        namespace,
                        parentComponent,
                    );
                }
            }
            if ("value" in newProps) {
                hostPatchProp(
                    el,
                    "value",
                    oldProps.value,
                    newProps.value,
                    namespace,
                );
            }
        }
    };
    const processFragment = (
        n1,
        n2,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
    ) => {
        const fragmentStartAnchor = (n2.el = n1 ? n1.el : hostCreateText(""));
        const fragmentEndAnchor = (n2.anchor = n1
            ? n1.anchor
            : hostCreateText(""));
        let {
            patchFlag,
            dynamicChildren,
            slotScopeIds: fragmentSlotScopeIds,
        } = n2;
        if (fragmentSlotScopeIds) {
            slotScopeIds = slotScopeIds
                ? slotScopeIds.concat(fragmentSlotScopeIds)
                : fragmentSlotScopeIds;
        }
        if (n1 == null) {
            hostInsert(fragmentStartAnchor, container, anchor);
            hostInsert(fragmentEndAnchor, container, anchor);
            mountChildren(
                // #10007
                // such fragment like `<></>` will be compiled into
                // a fragment which doesn't have a children.
                // In this case fallback to an empty array
                n2.children || [],
                container,
                fragmentEndAnchor,
                parentComponent,
                parentSuspense,
                namespace,
                slotScopeIds,
                optimized,
            );
        } else {
            if (
                patchFlag > 0 &&
                patchFlag & 64 &&
                dynamicChildren && // #2715 the previous fragment could've been a BAILed one as a result
                // of renderSlot() with no valid children
                n1.dynamicChildren
            ) {
                patchBlockChildren(
                    n1.dynamicChildren,
                    dynamicChildren,
                    container,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    slotScopeIds,
                );
                if (
                    // #2080 if the stable fragment has a key, it's a <template v-for> that may
                    //  get moved around. Make sure all root level vnodes inherit el.
                    // #2134 or if it's a component root, it may also get moved around
                    // as the component is being moved.
                    n2.key != null ||
                    (parentComponent && n2 === parentComponent.subTree)
                ) {
                    traverseStaticChildren(
                        n1,
                        n2,
                        true,
                        /* shallow */
                    );
                }
            } else {
                patchChildren(
                    n1,
                    n2,
                    container,
                    fragmentEndAnchor,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    slotScopeIds,
                    optimized,
                );
            }
        }
    };
    const processComponent = (
        n1,
        n2,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
    ) => {
        n2.slotScopeIds = slotScopeIds;
        if (n1 == null) {
            if (n2.shapeFlag & 512) {
                parentComponent.ctx.activate(
                    n2,
                    container,
                    anchor,
                    namespace,
                    optimized,
                );
            } else {
                mountComponent(
                    n2,
                    container,
                    anchor,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    optimized,
                );
            }
        } else {
            updateComponent(n1, n2, optimized);
        }
    };
    const mountComponent = (
        initialVNode,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        optimized,
    ) => {
        const instance = (initialVNode.component = createComponentInstance(
            initialVNode,
            parentComponent,
            parentSuspense,
        ));
        if (isKeepAlive(initialVNode)) {
            instance.ctx.renderer = internals;
        }
        {
            setupComponent(instance, false, optimized);
        }
        if (instance.asyncDep) {
            parentSuspense &&
                parentSuspense.registerDep(
                    instance,
                    setupRenderEffect,
                    optimized,
                );
            if (!initialVNode.el) {
                const placeholder = (instance.subTree = createVNode(Comment));
                processCommentNode(null, placeholder, container, anchor);
            }
        } else {
            setupRenderEffect(
                instance,
                initialVNode,
                container,
                anchor,
                parentSuspense,
                namespace,
                optimized,
            );
        }
    };
    const updateComponent = (n1, n2, optimized) => {
        const instance = (n2.component = n1.component);
        if (shouldUpdateComponent(n1, n2, optimized)) {
            if (instance.asyncDep && !instance.asyncResolved) {
                updateComponentPreRender(instance, n2, optimized);
                return;
            } else {
                instance.next = n2;
                instance.update();
            }
        } else {
            n2.el = n1.el;
            instance.vnode = n2;
        }
    };
    const setupRenderEffect = (
        instance,
        initialVNode,
        container,
        anchor,
        parentSuspense,
        namespace,
        optimized,
    ) => {
        const componentUpdateFn = () => {
            if (!instance.isMounted) {
                let vnodeHook;
                const { el, props } = initialVNode;
                const { bm, m, parent, root, type } = instance;
                const isAsyncWrapperVNode = isAsyncWrapper(initialVNode);
                toggleRecurse(instance, false);
                if (bm) {
                    invokeArrayFns(bm);
                }
                if (
                    !isAsyncWrapperVNode &&
                    (vnodeHook = props && props.onVnodeBeforeMount)
                ) {
                    invokeVNodeHook(vnodeHook, parent, initialVNode);
                }
                toggleRecurse(instance, true);
                {
                    if (root.ce) {
                        root.ce._injectChildStyle(type);
                    }
                    const subTree = (instance.subTree =
                        renderComponentRoot(instance));
                    patch(
                        null,
                        subTree,
                        container,
                        anchor,
                        instance,
                        parentSuspense,
                        namespace,
                    );
                    initialVNode.el = subTree.el;
                }
                if (m) {
                    queuePostRenderEffect(m, parentSuspense);
                }
                if (
                    !isAsyncWrapperVNode &&
                    (vnodeHook = props && props.onVnodeMounted)
                ) {
                    const scopedInitialVNode = initialVNode;
                    queuePostRenderEffect(
                        () =>
                            invokeVNodeHook(
                                vnodeHook,
                                parent,
                                scopedInitialVNode,
                            ),
                        parentSuspense,
                    );
                }
                if (
                    initialVNode.shapeFlag & 256 ||
                    (parent &&
                        isAsyncWrapper(parent.vnode) &&
                        parent.vnode.shapeFlag & 256)
                ) {
                    instance.a &&
                        queuePostRenderEffect(instance.a, parentSuspense);
                }
                instance.isMounted = true;
                initialVNode = container = anchor = null;
            } else {
                let { next, bu, u, parent, vnode } = instance;
                {
                    const nonHydratedAsyncRoot =
                        locateNonHydratedAsyncRoot(instance);
                    if (nonHydratedAsyncRoot) {
                        if (next) {
                            next.el = vnode.el;
                            updateComponentPreRender(instance, next, optimized);
                        }
                        nonHydratedAsyncRoot.asyncDep.then(() => {
                            if (!instance.isUnmounted) {
                                componentUpdateFn();
                            }
                        });
                        return;
                    }
                }
                let originNext = next;
                let vnodeHook;
                toggleRecurse(instance, false);
                if (next) {
                    next.el = vnode.el;
                    updateComponentPreRender(instance, next, optimized);
                } else {
                    next = vnode;
                }
                if (bu) {
                    invokeArrayFns(bu);
                }
                if (
                    (vnodeHook = next.props && next.props.onVnodeBeforeUpdate)
                ) {
                    invokeVNodeHook(vnodeHook, parent, next, vnode);
                }
                toggleRecurse(instance, true);
                const nextTree = renderComponentRoot(instance);
                const prevTree = instance.subTree;
                instance.subTree = nextTree;
                patch(
                    prevTree,
                    nextTree,
                    // parent may have changed if it's in a teleport
                    hostParentNode(prevTree.el),
                    // anchor may have changed if it's in a fragment
                    getNextHostNode(prevTree),
                    instance,
                    parentSuspense,
                    namespace,
                );
                next.el = nextTree.el;
                if (originNext === null) {
                    updateHOCHostEl(instance, nextTree.el);
                }
                if (u) {
                    queuePostRenderEffect(u, parentSuspense);
                }
                if ((vnodeHook = next.props && next.props.onVnodeUpdated)) {
                    queuePostRenderEffect(
                        () => invokeVNodeHook(vnodeHook, parent, next, vnode),
                        parentSuspense,
                    );
                }
            }
        };
        instance.scope.on();
        const effect2 = (instance.effect = new ReactiveEffect(
            componentUpdateFn,
        ));
        instance.scope.off();
        const update = (instance.update = effect2.run.bind(effect2));
        const job = (instance.job = effect2.runIfDirty.bind(effect2));
        job.i = instance;
        job.id = instance.uid;
        effect2.scheduler = () => queueJob(job);
        toggleRecurse(instance, true);
        update();
    };
    const updateComponentPreRender = (instance, nextVNode, optimized) => {
        nextVNode.component = instance;
        const prevProps = instance.vnode.props;
        instance.vnode = nextVNode;
        instance.next = null;
        updateProps(instance, nextVNode.props, prevProps, optimized);
        updateSlots(instance, nextVNode.children, optimized);
        pauseTracking();
        flushPreFlushCbs(instance);
        resetTracking();
    };
    const patchChildren = (
        n1,
        n2,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized = false,
    ) => {
        const c1 = n1 && n1.children;
        const prevShapeFlag = n1 ? n1.shapeFlag : 0;
        const c2 = n2.children;
        const { patchFlag, shapeFlag } = n2;
        if (patchFlag > 0) {
            if (patchFlag & 128) {
                patchKeyedChildren(
                    c1,
                    c2,
                    container,
                    anchor,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    slotScopeIds,
                    optimized,
                );
                return;
            } else if (patchFlag & 256) {
                patchUnkeyedChildren(
                    c1,
                    c2,
                    container,
                    anchor,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    slotScopeIds,
                    optimized,
                );
                return;
            }
        }
        if (shapeFlag & 8) {
            if (prevShapeFlag & 16) {
                unmountChildren(c1, parentComponent, parentSuspense);
            }
            if (c2 !== c1) {
                hostSetElementText(container, c2);
            }
        } else {
            if (prevShapeFlag & 16) {
                if (shapeFlag & 16) {
                    patchKeyedChildren(
                        c1,
                        c2,
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                    );
                } else {
                    unmountChildren(c1, parentComponent, parentSuspense, true);
                }
            } else {
                if (prevShapeFlag & 8) {
                    hostSetElementText(container, "");
                }
                if (shapeFlag & 16) {
                    mountChildren(
                        c2,
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                    );
                }
            }
        }
    };
    const patchUnkeyedChildren = (
        c1,
        c2,
        container,
        anchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
    ) => {
        c1 = c1 || EMPTY_ARR;
        c2 = c2 || EMPTY_ARR;
        const oldLength = c1.length;
        const newLength = c2.length;
        const commonLength = Math.min(oldLength, newLength);
        let i;
        for (i = 0; i < commonLength; i++) {
            const nextChild = (c2[i] = optimized
                ? cloneIfMounted(c2[i])
                : normalizeVNode(c2[i]));
            patch(
                c1[i],
                nextChild,
                container,
                null,
                parentComponent,
                parentSuspense,
                namespace,
                slotScopeIds,
                optimized,
            );
        }
        if (oldLength > newLength) {
            unmountChildren(
                c1,
                parentComponent,
                parentSuspense,
                true,
                false,
                commonLength,
            );
        } else {
            mountChildren(
                c2,
                container,
                anchor,
                parentComponent,
                parentSuspense,
                namespace,
                slotScopeIds,
                optimized,
                commonLength,
            );
        }
    };
    const patchKeyedChildren = (
        c1,
        c2,
        container,
        parentAnchor,
        parentComponent,
        parentSuspense,
        namespace,
        slotScopeIds,
        optimized,
    ) => {
        let i = 0;
        const l2 = c2.length;
        let e1 = c1.length - 1;
        let e2 = l2 - 1;
        while (i <= e1 && i <= e2) {
            const n1 = c1[i];
            const n2 = (c2[i] = optimized
                ? cloneIfMounted(c2[i])
                : normalizeVNode(c2[i]));
            if (isSameVNodeType(n1, n2)) {
                patch(
                    n1,
                    n2,
                    container,
                    null,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    slotScopeIds,
                    optimized,
                );
            } else {
                break;
            }
            i++;
        }
        while (i <= e1 && i <= e2) {
            const n1 = c1[e1];
            const n2 = (c2[e2] = optimized
                ? cloneIfMounted(c2[e2])
                : normalizeVNode(c2[e2]));
            if (isSameVNodeType(n1, n2)) {
                patch(
                    n1,
                    n2,
                    container,
                    null,
                    parentComponent,
                    parentSuspense,
                    namespace,
                    slotScopeIds,
                    optimized,
                );
            } else {
                break;
            }
            e1--;
            e2--;
        }
        if (i > e1) {
            if (i <= e2) {
                const nextPos = e2 + 1;
                const anchor = nextPos < l2 ? c2[nextPos].el : parentAnchor;
                while (i <= e2) {
                    patch(
                        null,
                        (c2[i] = optimized
                            ? cloneIfMounted(c2[i])
                            : normalizeVNode(c2[i])),
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                    );
                    i++;
                }
            }
        } else if (i > e2) {
            while (i <= e1) {
                unmount(c1[i], parentComponent, parentSuspense, true);
                i++;
            }
        } else {
            const s1 = i;
            const s2 = i;
            const keyToNewIndexMap = /* @__PURE__ */ new Map();
            for (i = s2; i <= e2; i++) {
                const nextChild = (c2[i] = optimized
                    ? cloneIfMounted(c2[i])
                    : normalizeVNode(c2[i]));
                if (nextChild.key != null) {
                    keyToNewIndexMap.set(nextChild.key, i);
                }
            }
            let j;
            let patched = 0;
            const toBePatched = e2 - s2 + 1;
            let moved = false;
            let maxNewIndexSoFar = 0;
            const newIndexToOldIndexMap = new Array(toBePatched);
            for (i = 0; i < toBePatched; i++) newIndexToOldIndexMap[i] = 0;
            for (i = s1; i <= e1; i++) {
                const prevChild = c1[i];
                if (patched >= toBePatched) {
                    unmount(prevChild, parentComponent, parentSuspense, true);
                    continue;
                }
                let newIndex;
                if (prevChild.key != null) {
                    newIndex = keyToNewIndexMap.get(prevChild.key);
                } else {
                    for (j = s2; j <= e2; j++) {
                        if (
                            newIndexToOldIndexMap[j - s2] === 0 &&
                            isSameVNodeType(prevChild, c2[j])
                        ) {
                            newIndex = j;
                            break;
                        }
                    }
                }
                if (newIndex === void 0) {
                    unmount(prevChild, parentComponent, parentSuspense, true);
                } else {
                    newIndexToOldIndexMap[newIndex - s2] = i + 1;
                    if (newIndex >= maxNewIndexSoFar) {
                        maxNewIndexSoFar = newIndex;
                    } else {
                        moved = true;
                    }
                    patch(
                        prevChild,
                        c2[newIndex],
                        container,
                        null,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                    );
                    patched++;
                }
            }
            const increasingNewIndexSequence = moved
                ? getSequence(newIndexToOldIndexMap)
                : EMPTY_ARR;
            j = increasingNewIndexSequence.length - 1;
            for (i = toBePatched - 1; i >= 0; i--) {
                const nextIndex = s2 + i;
                const nextChild = c2[nextIndex];
                const anchor =
                    nextIndex + 1 < l2 ? c2[nextIndex + 1].el : parentAnchor;
                if (newIndexToOldIndexMap[i] === 0) {
                    patch(
                        null,
                        nextChild,
                        container,
                        anchor,
                        parentComponent,
                        parentSuspense,
                        namespace,
                        slotScopeIds,
                        optimized,
                    );
                } else if (moved) {
                    if (j < 0 || i !== increasingNewIndexSequence[j]) {
                        move(nextChild, container, anchor, 2);
                    } else {
                        j--;
                    }
                }
            }
        }
    };
    const move = (
        vnode,
        container,
        anchor,
        moveType,
        parentSuspense = null,
    ) => {
        const { el, type, transition, children, shapeFlag } = vnode;
        if (shapeFlag & 6) {
            move(vnode.component.subTree, container, anchor, moveType);
            return;
        }
        if (shapeFlag & 128) {
            vnode.suspense.move(container, anchor, moveType);
            return;
        }
        if (shapeFlag & 64) {
            type.move(vnode, container, anchor, internals);
            return;
        }
        if (type === Fragment) {
            hostInsert(el, container, anchor);
            for (let i = 0; i < children.length; i++) {
                move(children[i], container, anchor, moveType);
            }
            hostInsert(vnode.anchor, container, anchor);
            return;
        }
        if (type === Static) {
            moveStaticNode(vnode, container, anchor);
            return;
        }
        const needTransition2 = moveType !== 2 && shapeFlag & 1 && transition;
        if (needTransition2) {
            if (moveType === 0) {
                transition.beforeEnter(el);
                hostInsert(el, container, anchor);
                queuePostRenderEffect(
                    () => transition.enter(el),
                    parentSuspense,
                );
            } else {
                const { leave, delayLeave, afterLeave } = transition;
                const remove22 = () => hostInsert(el, container, anchor);
                const performLeave = () => {
                    leave(el, () => {
                        remove22();
                        afterLeave && afterLeave();
                    });
                };
                if (delayLeave) {
                    delayLeave(el, remove22, performLeave);
                } else {
                    performLeave();
                }
            }
        } else {
            hostInsert(el, container, anchor);
        }
    };
    const unmount = (
        vnode,
        parentComponent,
        parentSuspense,
        doRemove = false,
        optimized = false,
    ) => {
        const {
            type,
            props,
            ref: ref3,
            children,
            dynamicChildren,
            shapeFlag,
            patchFlag,
            dirs,
            cacheIndex,
        } = vnode;
        if (patchFlag === -2) {
            optimized = false;
        }
        if (ref3 != null) {
            setRef(ref3, null, parentSuspense, vnode, true);
        }
        if (cacheIndex != null) {
            parentComponent.renderCache[cacheIndex] = void 0;
        }
        if (shapeFlag & 256) {
            parentComponent.ctx.deactivate(vnode);
            return;
        }
        const shouldInvokeDirs = shapeFlag & 1 && dirs;
        const shouldInvokeVnodeHook = !isAsyncWrapper(vnode);
        let vnodeHook;
        if (
            shouldInvokeVnodeHook &&
            (vnodeHook = props && props.onVnodeBeforeUnmount)
        ) {
            invokeVNodeHook(vnodeHook, parentComponent, vnode);
        }
        if (shapeFlag & 6) {
            unmountComponent(vnode.component, parentSuspense, doRemove);
        } else {
            if (shapeFlag & 128) {
                vnode.suspense.unmount(parentSuspense, doRemove);
                return;
            }
            if (shouldInvokeDirs) {
                invokeDirectiveHook(
                    vnode,
                    null,
                    parentComponent,
                    "beforeUnmount",
                );
            }
            if (shapeFlag & 64) {
                vnode.type.remove(
                    vnode,
                    parentComponent,
                    parentSuspense,
                    internals,
                    doRemove,
                );
            } else if (
                dynamicChildren && // #5154
                // when v-once is used inside a block, setBlockTracking(-1) marks the
                // parent block with hasOnce: true
                // so that it doesn't take the fast path during unmount - otherwise
                // components nested in v-once are never unmounted.
                !dynamicChildren.hasOnce && // #1153: fast path should not be taken for non-stable (v-for) fragments
                (type !== Fragment || (patchFlag > 0 && patchFlag & 64))
            ) {
                unmountChildren(
                    dynamicChildren,
                    parentComponent,
                    parentSuspense,
                    false,
                    true,
                );
            } else if (
                (type === Fragment && patchFlag & (128 | 256)) ||
                (!optimized && shapeFlag & 16)
            ) {
                unmountChildren(children, parentComponent, parentSuspense);
            }
            if (doRemove) {
                remove2(vnode);
            }
        }
        if (
            (shouldInvokeVnodeHook &&
                (vnodeHook = props && props.onVnodeUnmounted)) ||
            shouldInvokeDirs
        ) {
            queuePostRenderEffect(() => {
                vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);
                shouldInvokeDirs &&
                    invokeDirectiveHook(
                        vnode,
                        null,
                        parentComponent,
                        "unmounted",
                    );
            }, parentSuspense);
        }
    };
    const remove2 = (vnode) => {
        const { type, el, anchor, transition } = vnode;
        if (type === Fragment) {
            {
                removeFragment(el, anchor);
            }
            return;
        }
        if (type === Static) {
            removeStaticNode(vnode);
            return;
        }
        const performRemove = () => {
            hostRemove(el);
            if (transition && !transition.persisted && transition.afterLeave) {
                transition.afterLeave();
            }
        };
        if (vnode.shapeFlag & 1 && transition && !transition.persisted) {
            const { leave, delayLeave } = transition;
            const performLeave = () => leave(el, performRemove);
            if (delayLeave) {
                delayLeave(vnode.el, performRemove, performLeave);
            } else {
                performLeave();
            }
        } else {
            performRemove();
        }
    };
    const removeFragment = (cur, end) => {
        let next;
        while (cur !== end) {
            next = hostNextSibling(cur);
            hostRemove(cur);
            cur = next;
        }
        hostRemove(end);
    };
    const unmountComponent = (instance, parentSuspense, doRemove) => {
        const { bum, scope, job, subTree, um, m, a } = instance;
        invalidateMount(m);
        invalidateMount(a);
        if (bum) {
            invokeArrayFns(bum);
        }
        scope.stop();
        if (job) {
            job.flags |= 8;
            unmount(subTree, instance, parentSuspense, doRemove);
        }
        if (um) {
            queuePostRenderEffect(um, parentSuspense);
        }
        queuePostRenderEffect(() => {
            instance.isUnmounted = true;
        }, parentSuspense);
        if (
            parentSuspense &&
            parentSuspense.pendingBranch &&
            !parentSuspense.isUnmounted &&
            instance.asyncDep &&
            !instance.asyncResolved &&
            instance.suspenseId === parentSuspense.pendingId
        ) {
            parentSuspense.deps--;
            if (parentSuspense.deps === 0) {
                parentSuspense.resolve();
            }
        }
    };
    const unmountChildren = (
        children,
        parentComponent,
        parentSuspense,
        doRemove = false,
        optimized = false,
        start = 0,
    ) => {
        for (let i = start; i < children.length; i++) {
            unmount(
                children[i],
                parentComponent,
                parentSuspense,
                doRemove,
                optimized,
            );
        }
    };
    const getNextHostNode = (vnode) => {
        if (vnode.shapeFlag & 6) {
            return getNextHostNode(vnode.component.subTree);
        }
        if (vnode.shapeFlag & 128) {
            return vnode.suspense.next();
        }
        const el = hostNextSibling(vnode.anchor || vnode.el);
        const teleportEnd = el && el[TeleportEndKey];
        return teleportEnd ? hostNextSibling(teleportEnd) : el;
    };
    let isFlushing = false;
    const render = (vnode, container, namespace) => {
        if (vnode == null) {
            if (container._vnode) {
                unmount(container._vnode, null, null, true);
            }
        } else {
            patch(
                container._vnode || null,
                vnode,
                container,
                null,
                null,
                null,
                namespace,
            );
        }
        container._vnode = vnode;
        if (!isFlushing) {
            isFlushing = true;
            flushPreFlushCbs();
            flushPostFlushCbs();
            isFlushing = false;
        }
    };
    const internals = {
        p: patch,
        um: unmount,
        m: move,
        r: remove2,
        mt: mountComponent,
        mc: mountChildren,
        pc: patchChildren,
        pbc: patchBlockChildren,
        n: getNextHostNode,
        o: options,
    };
    let hydrate;
    return {
        render,
        hydrate,
        createApp: createAppAPI(render),
    };
}
function resolveChildrenNamespace({ type, props }, currentNamespace) {
    return (currentNamespace === "svg" && type === "foreignObject") ||
        (currentNamespace === "mathml" &&
            type === "annotation-xml" &&
            props &&
            props.encoding &&
            props.encoding.includes("html"))
        ? void 0
        : currentNamespace;
}
function toggleRecurse({ effect: effect2, job }, allowed) {
    if (allowed) {
        effect2.flags |= 32;
        job.flags |= 4;
    } else {
        effect2.flags &= -33;
        job.flags &= -5;
    }
}
function needTransition(parentSuspense, transition) {
    return (
        (!parentSuspense ||
            (parentSuspense && !parentSuspense.pendingBranch)) &&
        transition &&
        !transition.persisted
    );
}
function traverseStaticChildren(n1, n2, shallow = false) {
    const ch1 = n1.children;
    const ch2 = n2.children;
    if (isArray(ch1) && isArray(ch2)) {
        for (let i = 0; i < ch1.length; i++) {
            const c1 = ch1[i];
            let c2 = ch2[i];
            if (c2.shapeFlag & 1 && !c2.dynamicChildren) {
                if (c2.patchFlag <= 0 || c2.patchFlag === 32) {
                    c2 = ch2[i] = cloneIfMounted(ch2[i]);
                    c2.el = c1.el;
                }
                if (!shallow && c2.patchFlag !== -2)
                    traverseStaticChildren(c1, c2);
            }
            if (c2.type === Text) {
                c2.el = c1.el;
            }
        }
    }
}
function getSequence(arr) {
    const p2 = arr.slice();
    const result = [0];
    let i, j, u, v, c;
    const len = arr.length;
    for (i = 0; i < len; i++) {
        const arrI = arr[i];
        if (arrI !== 0) {
            j = result[result.length - 1];
            if (arr[j] < arrI) {
                p2[i] = j;
                result.push(i);
                continue;
            }
            u = 0;
            v = result.length - 1;
            while (u < v) {
                c = (u + v) >> 1;
                if (arr[result[c]] < arrI) {
                    u = c + 1;
                } else {
                    v = c;
                }
            }
            if (arrI < arr[result[u]]) {
                if (u > 0) {
                    p2[i] = result[u - 1];
                }
                result[u] = i;
            }
        }
    }
    u = result.length;
    v = result[u - 1];
    while (u-- > 0) {
        result[u] = v;
        v = p2[v];
    }
    return result;
}
function locateNonHydratedAsyncRoot(instance) {
    const subComponent = instance.subTree.component;
    if (subComponent) {
        if (subComponent.asyncDep && !subComponent.asyncResolved) {
            return subComponent;
        } else {
            return locateNonHydratedAsyncRoot(subComponent);
        }
    }
}
function invalidateMount(hooks) {
    if (hooks) {
        for (let i = 0; i < hooks.length; i++) hooks[i].flags |= 8;
    }
}
const ssrContextKey = Symbol.for("v-scx");
const useSSRContext = () => {
    {
        const ctx = inject(ssrContextKey);
        return ctx;
    }
};
function watch(source, cb, options) {
    return doWatch(source, cb, options);
}
function doWatch(source, cb, options = EMPTY_OBJ) {
    const { immediate, deep, flush, once } = options;
    const baseWatchOptions = extend({}, options);
    const runsImmediately = (cb && immediate) || (!cb && flush !== "post");
    let ssrCleanup;
    if (isInSSRComponentSetup) {
        if (flush === "sync") {
            const ctx = useSSRContext();
            ssrCleanup = ctx.__watcherHandles || (ctx.__watcherHandles = []);
        } else if (!runsImmediately) {
            const watchStopHandle = () => {};
            watchStopHandle.stop = NOOP;
            watchStopHandle.resume = NOOP;
            watchStopHandle.pause = NOOP;
            return watchStopHandle;
        }
    }
    const instance = currentInstance;
    baseWatchOptions.call = (fn, type, args) =>
        callWithAsyncErrorHandling(fn, instance, type, args);
    let isPre = false;
    if (flush === "post") {
        baseWatchOptions.scheduler = (job) => {
            queuePostRenderEffect(job, instance && instance.suspense);
        };
    } else if (flush !== "sync") {
        isPre = true;
        baseWatchOptions.scheduler = (job, isFirstRun) => {
            if (isFirstRun) {
                job();
            } else {
                queueJob(job);
            }
        };
    }
    baseWatchOptions.augmentJob = (job) => {
        if (cb) {
            job.flags |= 4;
        }
        if (isPre) {
            job.flags |= 2;
            if (instance) {
                job.id = instance.uid;
                job.i = instance;
            }
        }
    };
    const watchHandle = watch$1(source, cb, baseWatchOptions);
    if (isInSSRComponentSetup) {
        if (ssrCleanup) {
            ssrCleanup.push(watchHandle);
        } else if (runsImmediately) {
            watchHandle();
        }
    }
    return watchHandle;
}
function instanceWatch(source, value, options) {
    const publicThis = this.proxy;
    const getter = isString(source)
        ? source.includes(".")
            ? createPathGetter(publicThis, source)
            : () => publicThis[source]
        : source.bind(publicThis, publicThis);
    let cb;
    if (isFunction(value)) {
        cb = value;
    } else {
        cb = value.handler;
        options = value;
    }
    const reset = setCurrentInstance(this);
    const res = doWatch(getter, cb.bind(publicThis), options);
    reset();
    return res;
}
function createPathGetter(ctx, path) {
    const segments = path.split(".");
    return () => {
        let cur = ctx;
        for (let i = 0; i < segments.length && cur; i++) {
            cur = cur[segments[i]];
        }
        return cur;
    };
}
const getModelModifiers = (props, modelName) => {
    return modelName === "modelValue" || modelName === "model-value"
        ? props.modelModifiers
        : props[`${modelName}Modifiers`] ||
              props[`${camelize(modelName)}Modifiers`] ||
              props[`${hyphenate(modelName)}Modifiers`];
};
function emit(instance, event, ...rawArgs) {
    if (instance.isUnmounted) return;
    const props = instance.vnode.props || EMPTY_OBJ;
    let args = rawArgs;
    const isModelListener2 = event.startsWith("update:");
    const modifiers =
        isModelListener2 && getModelModifiers(props, event.slice(7));
    if (modifiers) {
        if (modifiers.trim) {
            args = rawArgs.map((a) => (isString(a) ? a.trim() : a));
        }
        if (modifiers.number) {
            args = rawArgs.map(looseToNumber);
        }
    }
    let handlerName;
    let handler =
        props[(handlerName = toHandlerKey(event))] || // also try camelCase event handler (#2249)
        props[(handlerName = toHandlerKey(camelize(event)))];
    if (!handler && isModelListener2) {
        handler = props[(handlerName = toHandlerKey(hyphenate(event)))];
    }
    if (handler) {
        callWithAsyncErrorHandling(handler, instance, 6, args);
    }
    const onceHandler = props[handlerName + `Once`];
    if (onceHandler) {
        if (!instance.emitted) {
            instance.emitted = {};
        } else if (instance.emitted[handlerName]) {
            return;
        }
        instance.emitted[handlerName] = true;
        callWithAsyncErrorHandling(onceHandler, instance, 6, args);
    }
}
function normalizeEmitsOptions(comp, appContext, asMixin = false) {
    const cache = appContext.emitsCache;
    const cached = cache.get(comp);
    if (cached !== void 0) {
        return cached;
    }
    const raw = comp.emits;
    let normalized = {};
    let hasExtends = false;
    if (!isFunction(comp)) {
        const extendEmits = (raw2) => {
            const normalizedFromExtend = normalizeEmitsOptions(
                raw2,
                appContext,
                true,
            );
            if (normalizedFromExtend) {
                hasExtends = true;
                extend(normalized, normalizedFromExtend);
            }
        };
        if (!asMixin && appContext.mixins.length) {
            appContext.mixins.forEach(extendEmits);
        }
        if (comp.extends) {
            extendEmits(comp.extends);
        }
        if (comp.mixins) {
            comp.mixins.forEach(extendEmits);
        }
    }
    if (!raw && !hasExtends) {
        if (isObject(comp)) {
            cache.set(comp, null);
        }
        return null;
    }
    if (isArray(raw)) {
        raw.forEach((key) => (normalized[key] = null));
    } else {
        extend(normalized, raw);
    }
    if (isObject(comp)) {
        cache.set(comp, normalized);
    }
    return normalized;
}
function isEmitListener(options, key) {
    if (!options || !isOn(key)) {
        return false;
    }
    key = key.slice(2).replace(/Once$/, "");
    return (
        hasOwn(options, key[0].toLowerCase() + key.slice(1)) ||
        hasOwn(options, hyphenate(key)) ||
        hasOwn(options, key)
    );
}
function markAttrsAccessed() {}
function renderComponentRoot(instance) {
    const {
        type: Component,
        vnode,
        proxy,
        withProxy,
        propsOptions: [propsOptions],
        slots,
        attrs,
        emit: emit2,
        render,
        renderCache,
        props,
        data,
        setupState,
        ctx,
        inheritAttrs,
    } = instance;
    const prev = setCurrentRenderingInstance(instance);
    let result;
    let fallthroughAttrs;
    try {
        if (vnode.shapeFlag & 4) {
            const proxyToUse = withProxy || proxy;
            const thisProxy = false
                ? new Proxy(proxyToUse, {
                      get(target, key, receiver) {
                          warn$1(
                              `Property '${String(
                                  key,
                              )}' was accessed via 'this'. Avoid using 'this' in templates.`,
                          );
                          return Reflect.get(target, key, receiver);
                      },
                  })
                : proxyToUse;
            result = normalizeVNode(
                render.call(
                    thisProxy,
                    proxyToUse,
                    renderCache,
                    false ? shallowReadonly(props) : props,
                    setupState,
                    data,
                    ctx,
                ),
            );
            fallthroughAttrs = attrs;
        } else {
            const render2 = Component;
            if (false);
            result = normalizeVNode(
                render2.length > 1
                    ? render2(
                          false ? shallowReadonly(props) : props,
                          false
                              ? {
                                    get attrs() {
                                        markAttrsAccessed();
                                        return shallowReadonly(attrs);
                                    },
                                    slots,
                                    emit: emit2,
                                }
                              : { attrs, slots, emit: emit2 },
                      )
                    : render2(false ? shallowReadonly(props) : props, null),
            );
            fallthroughAttrs = Component.props
                ? attrs
                : getFunctionalFallthrough(attrs);
        }
    } catch (err) {
        blockStack.length = 0;
        handleError(err, instance, 1);
        result = createVNode(Comment);
    }
    let root = result;
    if (fallthroughAttrs && inheritAttrs !== false) {
        const keys = Object.keys(fallthroughAttrs);
        const { shapeFlag } = root;
        if (keys.length) {
            if (shapeFlag & (1 | 6)) {
                if (propsOptions && keys.some(isModelListener)) {
                    fallthroughAttrs = filterModelListeners(
                        fallthroughAttrs,
                        propsOptions,
                    );
                }
                root = cloneVNode(root, fallthroughAttrs, false, true);
            }
        }
    }
    if (vnode.dirs) {
        root = cloneVNode(root, null, false, true);
        root.dirs = root.dirs ? root.dirs.concat(vnode.dirs) : vnode.dirs;
    }
    if (vnode.transition) {
        setTransitionHooks(root, vnode.transition);
    }
    {
        result = root;
    }
    setCurrentRenderingInstance(prev);
    return result;
}
const getFunctionalFallthrough = (attrs) => {
    let res;
    for (const key in attrs) {
        if (key === "class" || key === "style" || isOn(key)) {
            (res || (res = {}))[key] = attrs[key];
        }
    }
    return res;
};
const filterModelListeners = (attrs, props) => {
    const res = {};
    for (const key in attrs) {
        if (!isModelListener(key) || !(key.slice(9) in props)) {
            res[key] = attrs[key];
        }
    }
    return res;
};
function shouldUpdateComponent(prevVNode, nextVNode, optimized) {
    const { props: prevProps, children: prevChildren, component } = prevVNode;
    const { props: nextProps, children: nextChildren, patchFlag } = nextVNode;
    const emits = component.emitsOptions;
    if (nextVNode.dirs || nextVNode.transition) {
        return true;
    }
    if (optimized && patchFlag >= 0) {
        if (patchFlag & 1024) {
            return true;
        }
        if (patchFlag & 16) {
            if (!prevProps) {
                return !!nextProps;
            }
            return hasPropsChanged(prevProps, nextProps, emits);
        } else if (patchFlag & 8) {
            const dynamicProps = nextVNode.dynamicProps;
            for (let i = 0; i < dynamicProps.length; i++) {
                const key = dynamicProps[i];
                if (
                    nextProps[key] !== prevProps[key] &&
                    !isEmitListener(emits, key)
                ) {
                    return true;
                }
            }
        }
    } else {
        if (prevChildren || nextChildren) {
            if (!nextChildren || !nextChildren.$stable) {
                return true;
            }
        }
        if (prevProps === nextProps) {
            return false;
        }
        if (!prevProps) {
            return !!nextProps;
        }
        if (!nextProps) {
            return true;
        }
        return hasPropsChanged(prevProps, nextProps, emits);
    }
    return false;
}
function hasPropsChanged(prevProps, nextProps, emitsOptions) {
    const nextKeys = Object.keys(nextProps);
    if (nextKeys.length !== Object.keys(prevProps).length) {
        return true;
    }
    for (let i = 0; i < nextKeys.length; i++) {
        const key = nextKeys[i];
        if (
            nextProps[key] !== prevProps[key] &&
            !isEmitListener(emitsOptions, key)
        ) {
            return true;
        }
    }
    return false;
}
function updateHOCHostEl({ vnode, parent }, el) {
    while (parent) {
        const root = parent.subTree;
        if (root.suspense && root.suspense.activeBranch === vnode) {
            root.el = vnode.el;
        }
        if (root === vnode) {
            (vnode = parent.vnode).el = el;
            parent = parent.parent;
        } else {
            break;
        }
    }
}
const isSuspense = (type) => type.__isSuspense;
function queueEffectWithSuspense(fn, suspense) {
    if (suspense && suspense.pendingBranch) {
        if (isArray(fn)) {
            suspense.effects.push(...fn);
        } else {
            suspense.effects.push(fn);
        }
    } else {
        queuePostFlushCb(fn);
    }
}
const Fragment = Symbol.for("v-fgt");
const Text = Symbol.for("v-txt");
const Comment = Symbol.for("v-cmt");
const Static = Symbol.for("v-stc");
const blockStack = [];
let currentBlock = null;
function openBlock(disableTracking = false) {
    blockStack.push((currentBlock = disableTracking ? null : []));
}
function closeBlock() {
    blockStack.pop();
    currentBlock = blockStack[blockStack.length - 1] || null;
}
let isBlockTreeEnabled = 1;
function setBlockTracking(value, inVOnce = false) {
    isBlockTreeEnabled += value;
    if (value < 0 && currentBlock && inVOnce) {
        currentBlock.hasOnce = true;
    }
}
function setupBlock(vnode) {
    vnode.dynamicChildren =
        isBlockTreeEnabled > 0 ? currentBlock || EMPTY_ARR : null;
    closeBlock();
    if (isBlockTreeEnabled > 0 && currentBlock) {
        currentBlock.push(vnode);
    }
    return vnode;
}
function createElementBlock(
    type,
    props,
    children,
    patchFlag,
    dynamicProps,
    shapeFlag,
) {
    return setupBlock(
        createBaseVNode(
            type,
            props,
            children,
            patchFlag,
            dynamicProps,
            shapeFlag,
            true,
        ),
    );
}
function createBlock(type, props, children, patchFlag, dynamicProps) {
    return setupBlock(
        createVNode(type, props, children, patchFlag, dynamicProps, true),
    );
}
function isVNode(value) {
    return value ? value.__v_isVNode === true : false;
}
function isSameVNodeType(n1, n2) {
    return n1.type === n2.type && n1.key === n2.key;
}
const normalizeKey = ({ key }) => (key != null ? key : null);
const normalizeRef = ({ ref: ref3, ref_key, ref_for }) => {
    if (typeof ref3 === "number") {
        ref3 = "" + ref3;
    }
    return ref3 != null
        ? isString(ref3) || isRef(ref3) || isFunction(ref3)
            ? { i: currentRenderingInstance, r: ref3, k: ref_key, f: !!ref_for }
            : ref3
        : null;
};
function createBaseVNode(
    type,
    props = null,
    children = null,
    patchFlag = 0,
    dynamicProps = null,
    shapeFlag = type === Fragment ? 0 : 1,
    isBlockNode = false,
    needFullChildrenNormalization = false,
) {
    const vnode = {
        __v_isVNode: true,
        __v_skip: true,
        type,
        props,
        key: props && normalizeKey(props),
        ref: props && normalizeRef(props),
        scopeId: currentScopeId,
        slotScopeIds: null,
        children,
        component: null,
        suspense: null,
        ssContent: null,
        ssFallback: null,
        dirs: null,
        transition: null,
        el: null,
        anchor: null,
        target: null,
        targetStart: null,
        targetAnchor: null,
        staticCount: 0,
        shapeFlag,
        patchFlag,
        dynamicProps,
        dynamicChildren: null,
        appContext: null,
        ctx: currentRenderingInstance,
    };
    if (needFullChildrenNormalization) {
        normalizeChildren(vnode, children);
        if (shapeFlag & 128) {
            type.normalize(vnode);
        }
    } else if (children) {
        vnode.shapeFlag |= isString(children) ? 8 : 16;
    }
    if (
        isBlockTreeEnabled > 0 && // avoid a block node from tracking itself
        !isBlockNode && // has current parent block
        currentBlock && // presence of a patch flag indicates this node needs patching on updates.
        // component nodes also should always be patched, because even if the
        // component doesn't need to update, it needs to persist the instance on to
        // the next vnode so that it can be properly unmounted later.
        (vnode.patchFlag > 0 || shapeFlag & 6) && // the EVENTS flag is only for hydration and if it is the only flag, the
        // vnode should not be considered dynamic due to handler caching.
        vnode.patchFlag !== 32
    ) {
        currentBlock.push(vnode);
    }
    return vnode;
}
const createVNode = _createVNode;
function _createVNode(
    type,
    props = null,
    children = null,
    patchFlag = 0,
    dynamicProps = null,
    isBlockNode = false,
) {
    if (!type || type === NULL_DYNAMIC_COMPONENT) {
        type = Comment;
    }
    if (isVNode(type)) {
        const cloned = cloneVNode(
            type,
            props,
            true,
            /* mergeRef: true */
        );
        if (children) {
            normalizeChildren(cloned, children);
        }
        if (isBlockTreeEnabled > 0 && !isBlockNode && currentBlock) {
            if (cloned.shapeFlag & 6) {
                currentBlock[currentBlock.indexOf(type)] = cloned;
            } else {
                currentBlock.push(cloned);
            }
        }
        cloned.patchFlag = -2;
        return cloned;
    }
    if (isClassComponent(type)) {
        type = type.__vccOpts;
    }
    if (props) {
        props = guardReactiveProps(props);
        let { class: klass, style } = props;
        if (klass && !isString(klass)) {
            props.class = normalizeClass(klass);
        }
        if (isObject(style)) {
            if (isProxy(style) && !isArray(style)) {
                style = extend({}, style);
            }
            props.style = normalizeStyle(style);
        }
    }
    const shapeFlag = isString(type)
        ? 1
        : isSuspense(type)
          ? 128
          : isTeleport(type)
            ? 64
            : isObject(type)
              ? 4
              : isFunction(type)
                ? 2
                : 0;
    return createBaseVNode(
        type,
        props,
        children,
        patchFlag,
        dynamicProps,
        shapeFlag,
        isBlockNode,
        true,
    );
}
function guardReactiveProps(props) {
    if (!props) return null;
    return isProxy(props) || isInternalObject(props)
        ? extend({}, props)
        : props;
}
function cloneVNode(
    vnode,
    extraProps,
    mergeRef = false,
    cloneTransition = false,
) {
    const { props, ref: ref3, patchFlag, children, transition } = vnode;
    const mergedProps = extraProps
        ? mergeProps(props || {}, extraProps)
        : props;
    const cloned = {
        __v_isVNode: true,
        __v_skip: true,
        type: vnode.type,
        props: mergedProps,
        key: mergedProps && normalizeKey(mergedProps),
        ref:
            extraProps && extraProps.ref
                ? // #2078 in the case of <component :is="vnode" ref="extra"/>
                  // if the vnode itself already has a ref, cloneVNode will need to merge
                  // the refs so the single vnode can be set on multiple refs
                  mergeRef && ref3
                    ? isArray(ref3)
                        ? ref3.concat(normalizeRef(extraProps))
                        : [ref3, normalizeRef(extraProps)]
                    : normalizeRef(extraProps)
                : ref3,
        scopeId: vnode.scopeId,
        slotScopeIds: vnode.slotScopeIds,
        children,
        target: vnode.target,
        targetStart: vnode.targetStart,
        targetAnchor: vnode.targetAnchor,
        staticCount: vnode.staticCount,
        shapeFlag: vnode.shapeFlag,
        // if the vnode is cloned with extra props, we can no longer assume its
        // existing patch flag to be reliable and need to add the FULL_PROPS flag.
        // note: preserve flag for fragments since they use the flag for children
        // fast paths only.
        patchFlag:
            extraProps && vnode.type !== Fragment
                ? patchFlag === -1
                    ? 16
                    : patchFlag | 16
                : patchFlag,
        dynamicProps: vnode.dynamicProps,
        dynamicChildren: vnode.dynamicChildren,
        appContext: vnode.appContext,
        dirs: vnode.dirs,
        transition,
        // These should technically only be non-null on mounted VNodes. However,
        // they *should* be copied for kept-alive vnodes. So we just always copy
        // them since them being non-null during a mount doesn't affect the logic as
        // they will simply be overwritten.
        component: vnode.component,
        suspense: vnode.suspense,
        ssContent: vnode.ssContent && cloneVNode(vnode.ssContent),
        ssFallback: vnode.ssFallback && cloneVNode(vnode.ssFallback),
        el: vnode.el,
        anchor: vnode.anchor,
        ctx: vnode.ctx,
        ce: vnode.ce,
    };
    if (transition && cloneTransition) {
        setTransitionHooks(cloned, transition.clone(cloned));
    }
    return cloned;
}
function createTextVNode(text = " ", flag = 0) {
    return createVNode(Text, null, text, flag);
}
function createStaticVNode(content, numberOfNodes) {
    const vnode = createVNode(Static, null, content);
    vnode.staticCount = numberOfNodes;
    return vnode;
}
function createCommentVNode(text = "", asBlock = false) {
    return asBlock
        ? (openBlock(), createBlock(Comment, null, text))
        : createVNode(Comment, null, text);
}
function normalizeVNode(child) {
    if (child == null || typeof child === "boolean") {
        return createVNode(Comment);
    } else if (isArray(child)) {
        return createVNode(
            Fragment,
            null,
            // #3666, avoid reference pollution when reusing vnode
            child.slice(),
        );
    } else if (isVNode(child)) {
        return cloneIfMounted(child);
    } else {
        return createVNode(Text, null, String(child));
    }
}
function cloneIfMounted(child) {
    return (child.el === null && child.patchFlag !== -1) || child.memo
        ? child
        : cloneVNode(child);
}
function normalizeChildren(vnode, children) {
    let type = 0;
    const { shapeFlag } = vnode;
    if (children == null) {
        children = null;
    } else if (isArray(children)) {
        type = 16;
    } else if (typeof children === "object") {
        if (shapeFlag & (1 | 64)) {
            const slot = children.default;
            if (slot) {
                slot._c && (slot._d = false);
                normalizeChildren(vnode, slot());
                slot._c && (slot._d = true);
            }
            return;
        } else {
            type = 32;
            const slotFlag = children._;
            if (!slotFlag && !isInternalObject(children)) {
                children._ctx = currentRenderingInstance;
            } else if (slotFlag === 3 && currentRenderingInstance) {
                if (currentRenderingInstance.slots._ === 1) {
                    children._ = 1;
                } else {
                    children._ = 2;
                    vnode.patchFlag |= 1024;
                }
            }
        }
    } else if (isFunction(children)) {
        children = { default: children, _ctx: currentRenderingInstance };
        type = 32;
    } else {
        children = String(children);
        if (shapeFlag & 64) {
            type = 16;
            children = [createTextVNode(children)];
        } else {
            type = 8;
        }
    }
    vnode.children = children;
    vnode.shapeFlag |= type;
}
function mergeProps(...args) {
    const ret = {};
    for (let i = 0; i < args.length; i++) {
        const toMerge = args[i];
        for (const key in toMerge) {
            if (key === "class") {
                if (ret.class !== toMerge.class) {
                    ret.class = normalizeClass([ret.class, toMerge.class]);
                }
            } else if (key === "style") {
                ret.style = normalizeStyle([ret.style, toMerge.style]);
            } else if (isOn(key)) {
                const existing = ret[key];
                const incoming = toMerge[key];
                if (
                    incoming &&
                    existing !== incoming &&
                    !(isArray(existing) && existing.includes(incoming))
                ) {
                    ret[key] = existing
                        ? [].concat(existing, incoming)
                        : incoming;
                }
            } else if (key !== "") {
                ret[key] = toMerge[key];
            }
        }
    }
    return ret;
}
function invokeVNodeHook(hook, instance, vnode, prevVNode = null) {
    callWithAsyncErrorHandling(hook, instance, 7, [vnode, prevVNode]);
}
const emptyAppContext = createAppContext();
let uid = 0;
function createComponentInstance(vnode, parent, suspense) {
    const type = vnode.type;
    const appContext =
        (parent ? parent.appContext : vnode.appContext) || emptyAppContext;
    const instance = {
        uid: uid++,
        vnode,
        type,
        parent,
        appContext,
        root: null,
        // to be immediately set
        next: null,
        subTree: null,
        // will be set synchronously right after creation
        effect: null,
        update: null,
        // will be set synchronously right after creation
        job: null,
        scope: new EffectScope(
            true,
            /* detached */
        ),
        render: null,
        proxy: null,
        exposed: null,
        exposeProxy: null,
        withProxy: null,
        provides: parent ? parent.provides : Object.create(appContext.provides),
        ids: parent ? parent.ids : ["", 0, 0],
        accessCache: null,
        renderCache: [],
        // local resolved assets
        components: null,
        directives: null,
        // resolved props and emits options
        propsOptions: normalizePropsOptions(type, appContext),
        emitsOptions: normalizeEmitsOptions(type, appContext),
        // emit
        emit: null,
        // to be set immediately
        emitted: null,
        // props default value
        propsDefaults: EMPTY_OBJ,
        // inheritAttrs
        inheritAttrs: type.inheritAttrs,
        // state
        ctx: EMPTY_OBJ,
        data: EMPTY_OBJ,
        props: EMPTY_OBJ,
        attrs: EMPTY_OBJ,
        slots: EMPTY_OBJ,
        refs: EMPTY_OBJ,
        setupState: EMPTY_OBJ,
        setupContext: null,
        // suspense related
        suspense,
        suspenseId: suspense ? suspense.pendingId : 0,
        asyncDep: null,
        asyncResolved: false,
        // lifecycle hooks
        // not using enums here because it results in computed properties
        isMounted: false,
        isUnmounted: false,
        isDeactivated: false,
        bc: null,
        c: null,
        bm: null,
        m: null,
        bu: null,
        u: null,
        um: null,
        bum: null,
        da: null,
        a: null,
        rtg: null,
        rtc: null,
        ec: null,
        sp: null,
    };
    {
        instance.ctx = { _: instance };
    }
    instance.root = parent ? parent.root : instance;
    instance.emit = emit.bind(null, instance);
    if (vnode.ce) {
        vnode.ce(instance);
    }
    return instance;
}
let currentInstance = null;
let internalSetCurrentInstance;
let setInSSRSetupState;
{
    const g = getGlobalThis();
    const registerGlobalSetter = (key, setter) => {
        let setters;
        if (!(setters = g[key])) setters = g[key] = [];
        setters.push(setter);
        return (v) => {
            if (setters.length > 1) setters.forEach((set2) => set2(v));
            else setters[0](v);
        };
    };
    internalSetCurrentInstance = registerGlobalSetter(
        `__VUE_INSTANCE_SETTERS__`,
        (v) => (currentInstance = v),
    );
    setInSSRSetupState = registerGlobalSetter(
        `__VUE_SSR_SETTERS__`,
        (v) => (isInSSRComponentSetup = v),
    );
}
const setCurrentInstance = (instance) => {
    const prev = currentInstance;
    internalSetCurrentInstance(instance);
    instance.scope.on();
    return () => {
        instance.scope.off();
        internalSetCurrentInstance(prev);
    };
};
const unsetCurrentInstance = () => {
    currentInstance && currentInstance.scope.off();
    internalSetCurrentInstance(null);
};
function isStatefulComponent(instance) {
    return instance.vnode.shapeFlag & 4;
}
let isInSSRComponentSetup = false;
function setupComponent(instance, isSSR = false, optimized = false) {
    isSSR && setInSSRSetupState(isSSR);
    const { props, children } = instance.vnode;
    const isStateful = isStatefulComponent(instance);
    initProps(instance, props, isStateful, isSSR);
    initSlots(instance, children, optimized);
    const setupResult = isStateful
        ? setupStatefulComponent(instance, isSSR)
        : void 0;
    isSSR && setInSSRSetupState(false);
    return setupResult;
}
function setupStatefulComponent(instance, isSSR) {
    const Component = instance.type;
    instance.accessCache = /* @__PURE__ */ Object.create(null);
    instance.proxy = new Proxy(instance.ctx, PublicInstanceProxyHandlers);
    const { setup } = Component;
    if (setup) {
        pauseTracking();
        const setupContext = (instance.setupContext =
            setup.length > 1 ? createSetupContext(instance) : null);
        const reset = setCurrentInstance(instance);
        const setupResult = callWithErrorHandling(setup, instance, 0, [
            instance.props,
            setupContext,
        ]);
        const isAsyncSetup = isPromise$1(setupResult);
        resetTracking();
        reset();
        if ((isAsyncSetup || instance.sp) && !isAsyncWrapper(instance)) {
            markAsyncBoundary(instance);
        }
        if (isAsyncSetup) {
            setupResult.then(unsetCurrentInstance, unsetCurrentInstance);
            if (isSSR) {
                return setupResult
                    .then((resolvedResult) => {
                        handleSetupResult(instance, resolvedResult);
                    })
                    .catch((e) => {
                        handleError(e, instance, 0);
                    });
            } else {
                instance.asyncDep = setupResult;
            }
        } else {
            handleSetupResult(instance, setupResult);
        }
    } else {
        finishComponentSetup(instance);
    }
}
function handleSetupResult(instance, setupResult, isSSR) {
    if (isFunction(setupResult)) {
        if (instance.type.__ssrInlineRender) {
            instance.ssrRender = setupResult;
        } else {
            instance.render = setupResult;
        }
    } else if (isObject(setupResult)) {
        instance.setupState = proxyRefs(setupResult);
    } else;
    finishComponentSetup(instance);
}
function finishComponentSetup(instance, isSSR, skipOptions) {
    const Component = instance.type;
    if (!instance.render) {
        instance.render = Component.render || NOOP;
    }
    {
        const reset = setCurrentInstance(instance);
        pauseTracking();
        try {
            applyOptions(instance);
        } finally {
            resetTracking();
            reset();
        }
    }
}
const attrsProxyHandlers = {
    get(target, key) {
        track(target, "get", "");
        return target[key];
    },
};
function createSetupContext(instance) {
    const expose = (exposed) => {
        instance.exposed = exposed || {};
    };
    {
        return {
            attrs: new Proxy(instance.attrs, attrsProxyHandlers),
            slots: instance.slots,
            emit: instance.emit,
            expose,
        };
    }
}
function getComponentPublicInstance(instance) {
    if (instance.exposed) {
        return (
            instance.exposeProxy ||
            (instance.exposeProxy = new Proxy(
                proxyRefs(markRaw(instance.exposed)),
                {
                    get(target, key) {
                        if (key in target) {
                            return target[key];
                        } else if (key in publicPropertiesMap) {
                            return publicPropertiesMap[key](instance);
                        }
                    },
                    has(target, key) {
                        return key in target || key in publicPropertiesMap;
                    },
                },
            ))
        );
    } else {
        return instance.proxy;
    }
}
const classifyRE = /(?:^|[-_])(\w)/g;
const classify = (str) =>
    str.replace(classifyRE, (c) => c.toUpperCase()).replace(/[-_]/g, "");
function getComponentName(Component, includeInferred = true) {
    return isFunction(Component)
        ? Component.displayName || Component.name
        : Component.name || (includeInferred && Component.__name);
}
function formatComponentName(instance, Component, isRoot = false) {
    let name = getComponentName(Component);
    if (!name && Component.__file) {
        const match = Component.__file.match(/([^/\\]+)\.\w+$/);
        if (match) {
            name = match[1];
        }
    }
    if (!name && instance && instance.parent) {
        const inferFromRegistry = (registry) => {
            for (const key in registry) {
                if (registry[key] === Component) {
                    return key;
                }
            }
        };
        name =
            inferFromRegistry(
                instance.components || instance.parent.type.components,
            ) || inferFromRegistry(instance.appContext.components);
    }
    return name ? classify(name) : isRoot ? `App` : `Anonymous`;
}
function isClassComponent(value) {
    return isFunction(value) && "__vccOpts" in value;
}
const computed = (getterOrOptions, debugOptions) => {
    const c = computed$1(getterOrOptions, debugOptions, isInSSRComponentSetup);
    return c;
};
const version$1 = "3.5.13";
/**
 * @vue/runtime-dom v3.5.13
 * (c) 2018-present Yuxi (Evan) You and Vue contributors
 * @license MIT
 **/
let policy = void 0;
const tt = typeof window !== "undefined" && window.trustedTypes;
if (tt) {
    try {
        policy = /* @__PURE__ */ tt.createPolicy("vue", {
            createHTML: (val) => val,
        });
    } catch (e) {}
}
const unsafeToTrustedHTML = policy
    ? (val) => policy.createHTML(val)
    : (val) => val;
const svgNS = "http://www.w3.org/2000/svg";
const mathmlNS = "http://www.w3.org/1998/Math/MathML";
const doc = typeof document !== "undefined" ? document : null;
const templateContainer = doc && /* @__PURE__ */ doc.createElement("template");
const nodeOps = {
    insert: (child, parent, anchor) => {
        parent.insertBefore(child, anchor || null);
    },
    remove: (child) => {
        const parent = child.parentNode;
        if (parent) {
            parent.removeChild(child);
        }
    },
    createElement: (tag, namespace, is, props) => {
        const el =
            namespace === "svg"
                ? doc.createElementNS(svgNS, tag)
                : namespace === "mathml"
                  ? doc.createElementNS(mathmlNS, tag)
                  : is
                    ? doc.createElement(tag, { is })
                    : doc.createElement(tag);
        if (tag === "select" && props && props.multiple != null) {
            el.setAttribute("multiple", props.multiple);
        }
        return el;
    },
    createText: (text) => doc.createTextNode(text),
    createComment: (text) => doc.createComment(text),
    setText: (node, text) => {
        node.nodeValue = text;
    },
    setElementText: (el, text) => {
        el.textContent = text;
    },
    parentNode: (node) => node.parentNode,
    nextSibling: (node) => node.nextSibling,
    querySelector: (selector) => doc.querySelector(selector),
    setScopeId(el, id) {
        el.setAttribute(id, "");
    },
    // __UNSAFE__
    // Reason: innerHTML.
    // Static content here can only come from compiled templates.
    // As long as the user only uses trusted templates, this is safe.
    insertStaticContent(content, parent, anchor, namespace, start, end) {
        const before = anchor ? anchor.previousSibling : parent.lastChild;
        if (start && (start === end || start.nextSibling)) {
            while (true) {
                parent.insertBefore(start.cloneNode(true), anchor);
                if (start === end || !(start = start.nextSibling)) break;
            }
        } else {
            templateContainer.innerHTML = unsafeToTrustedHTML(
                namespace === "svg"
                    ? `<svg>${content}</svg>`
                    : namespace === "mathml"
                      ? `<math>${content}</math>`
                      : content,
            );
            const template = templateContainer.content;
            if (namespace === "svg" || namespace === "mathml") {
                const wrapper = template.firstChild;
                while (wrapper.firstChild) {
                    template.appendChild(wrapper.firstChild);
                }
                template.removeChild(wrapper);
            }
            parent.insertBefore(template, anchor);
        }
        return [
            // first
            before ? before.nextSibling : parent.firstChild,
            // last
            anchor ? anchor.previousSibling : parent.lastChild,
        ];
    },
};
const vtcKey = Symbol("_vtc");
function patchClass(el, value, isSVG) {
    const transitionClasses = el[vtcKey];
    if (transitionClasses) {
        value = (
            value ? [value, ...transitionClasses] : [...transitionClasses]
        ).join(" ");
    }
    if (value == null) {
        el.removeAttribute("class");
    } else if (isSVG) {
        el.setAttribute("class", value);
    } else {
        el.className = value;
    }
}
const vShowOriginalDisplay = Symbol("_vod");
const vShowHidden = Symbol("_vsh");
const CSS_VAR_TEXT = Symbol("");
const displayRE = /(^|;)\s*display\s*:/;
function patchStyle(el, prev, next) {
    const style = el.style;
    const isCssString = isString(next);
    let hasControlledDisplay = false;
    if (next && !isCssString) {
        if (prev) {
            if (!isString(prev)) {
                for (const key in prev) {
                    if (next[key] == null) {
                        setStyle(style, key, "");
                    }
                }
            } else {
                for (const prevStyle of prev.split(";")) {
                    const key = prevStyle
                        .slice(0, prevStyle.indexOf(":"))
                        .trim();
                    if (next[key] == null) {
                        setStyle(style, key, "");
                    }
                }
            }
        }
        for (const key in next) {
            if (key === "display") {
                hasControlledDisplay = true;
            }
            setStyle(style, key, next[key]);
        }
    } else {
        if (isCssString) {
            if (prev !== next) {
                const cssVarText = style[CSS_VAR_TEXT];
                if (cssVarText) {
                    next += ";" + cssVarText;
                }
                style.cssText = next;
                hasControlledDisplay = displayRE.test(next);
            }
        } else if (prev) {
            el.removeAttribute("style");
        }
    }
    if (vShowOriginalDisplay in el) {
        el[vShowOriginalDisplay] = hasControlledDisplay ? style.display : "";
        if (el[vShowHidden]) {
            style.display = "none";
        }
    }
}
const importantRE = /\s*!important$/;
function setStyle(style, name, val) {
    if (isArray(val)) {
        val.forEach((v) => setStyle(style, name, v));
    } else {
        if (val == null) val = "";
        if (name.startsWith("--")) {
            style.setProperty(name, val);
        } else {
            const prefixed = autoPrefix(style, name);
            if (importantRE.test(val)) {
                style.setProperty(
                    hyphenate(prefixed),
                    val.replace(importantRE, ""),
                    "important",
                );
            } else {
                style[prefixed] = val;
            }
        }
    }
}
const prefixes = ["Webkit", "Moz", "ms"];
const prefixCache = {};
function autoPrefix(style, rawName) {
    const cached = prefixCache[rawName];
    if (cached) {
        return cached;
    }
    let name = camelize(rawName);
    if (name !== "filter" && name in style) {
        return (prefixCache[rawName] = name);
    }
    name = capitalize(name);
    for (let i = 0; i < prefixes.length; i++) {
        const prefixed = prefixes[i] + name;
        if (prefixed in style) {
            return (prefixCache[rawName] = prefixed);
        }
    }
    return rawName;
}
const xlinkNS = "http://www.w3.org/1999/xlink";
function patchAttr(
    el,
    key,
    value,
    isSVG,
    instance,
    isBoolean = isSpecialBooleanAttr(key),
) {
    if (isSVG && key.startsWith("xlink:")) {
        if (value == null) {
            el.removeAttributeNS(xlinkNS, key.slice(6, key.length));
        } else {
            el.setAttributeNS(xlinkNS, key, value);
        }
    } else {
        if (value == null || (isBoolean && !includeBooleanAttr(value))) {
            el.removeAttribute(key);
        } else {
            el.setAttribute(
                key,
                isBoolean ? "" : isSymbol(value) ? String(value) : value,
            );
        }
    }
}
function patchDOMProp(el, key, value, parentComponent, attrName) {
    if (key === "innerHTML" || key === "textContent") {
        if (value != null) {
            el[key] = key === "innerHTML" ? unsafeToTrustedHTML(value) : value;
        }
        return;
    }
    const tag = el.tagName;
    if (
        key === "value" &&
        tag !== "PROGRESS" && // custom elements may use _value internally
        !tag.includes("-")
    ) {
        const oldValue =
            tag === "OPTION" ? el.getAttribute("value") || "" : el.value;
        const newValue =
            value == null
                ? // #11647: value should be set as empty string for null and undefined,
                  // but <input type="checkbox"> should be set as 'on'.
                  el.type === "checkbox"
                    ? "on"
                    : ""
                : String(value);
        if (oldValue !== newValue || !("_value" in el)) {
            el.value = newValue;
        }
        if (value == null) {
            el.removeAttribute(key);
        }
        el._value = value;
        return;
    }
    let needRemove = false;
    if (value === "" || value == null) {
        const type = typeof el[key];
        if (type === "boolean") {
            value = includeBooleanAttr(value);
        } else if (value == null && type === "string") {
            value = "";
            needRemove = true;
        } else if (type === "number") {
            value = 0;
            needRemove = true;
        }
    }
    try {
        el[key] = value;
    } catch (e) {}
    needRemove && el.removeAttribute(attrName || key);
}
function addEventListener(el, event, handler, options) {
    el.addEventListener(event, handler, options);
}
function removeEventListener(el, event, handler, options) {
    el.removeEventListener(event, handler, options);
}
const veiKey = Symbol("_vei");
function patchEvent(el, rawName, prevValue, nextValue, instance = null) {
    const invokers = el[veiKey] || (el[veiKey] = {});
    const existingInvoker = invokers[rawName];
    if (nextValue && existingInvoker) {
        existingInvoker.value = nextValue;
    } else {
        const [name, options] = parseName(rawName);
        if (nextValue) {
            const invoker = (invokers[rawName] = createInvoker(
                nextValue,
                instance,
            ));
            addEventListener(el, name, invoker, options);
        } else if (existingInvoker) {
            removeEventListener(el, name, existingInvoker, options);
            invokers[rawName] = void 0;
        }
    }
}
const optionsModifierRE = /(?:Once|Passive|Capture)$/;
function parseName(name) {
    let options;
    if (optionsModifierRE.test(name)) {
        options = {};
        let m;
        while ((m = name.match(optionsModifierRE))) {
            name = name.slice(0, name.length - m[0].length);
            options[m[0].toLowerCase()] = true;
        }
    }
    const event = name[2] === ":" ? name.slice(3) : hyphenate(name.slice(2));
    return [event, options];
}
let cachedNow = 0;
const p = /* @__PURE__ */ Promise.resolve();
const getNow = () =>
    cachedNow || (p.then(() => (cachedNow = 0)), (cachedNow = Date.now()));
function createInvoker(initialValue, instance) {
    const invoker = (e) => {
        if (!e._vts) {
            e._vts = Date.now();
        } else if (e._vts <= invoker.attached) {
            return;
        }
        callWithAsyncErrorHandling(
            patchStopImmediatePropagation(e, invoker.value),
            instance,
            5,
            [e],
        );
    };
    invoker.value = initialValue;
    invoker.attached = getNow();
    return invoker;
}
function patchStopImmediatePropagation(e, value) {
    if (isArray(value)) {
        const originalStop = e.stopImmediatePropagation;
        e.stopImmediatePropagation = () => {
            originalStop.call(e);
            e._stopped = true;
        };
        return value.map((fn) => (e2) => !e2._stopped && fn && fn(e2));
    } else {
        return value;
    }
}
const isNativeOn = (key) =>
    key.charCodeAt(0) === 111 &&
    key.charCodeAt(1) === 110 && // lowercase letter
    key.charCodeAt(2) > 96 &&
    key.charCodeAt(2) < 123;
const patchProp = (
    el,
    key,
    prevValue,
    nextValue,
    namespace,
    parentComponent,
) => {
    const isSVG = namespace === "svg";
    if (key === "class") {
        patchClass(el, nextValue, isSVG);
    } else if (key === "style") {
        patchStyle(el, prevValue, nextValue);
    } else if (isOn(key)) {
        if (!isModelListener(key)) {
            patchEvent(el, key, prevValue, nextValue, parentComponent);
        }
    } else if (
        key[0] === "."
            ? ((key = key.slice(1)), true)
            : key[0] === "^"
              ? ((key = key.slice(1)), false)
              : shouldSetAsProp(el, key, nextValue, isSVG)
    ) {
        patchDOMProp(el, key, nextValue);
        if (
            !el.tagName.includes("-") &&
            (key === "value" || key === "checked" || key === "selected")
        ) {
            patchAttr(
                el,
                key,
                nextValue,
                isSVG,
                parentComponent,
                key !== "value",
            );
        }
    } else if (
        // #11081 force set props for possible async custom element
        el._isVueCE &&
        (/[A-Z]/.test(key) || !isString(nextValue))
    ) {
        patchDOMProp(el, camelize(key), nextValue, parentComponent, key);
    } else {
        if (key === "true-value") {
            el._trueValue = nextValue;
        } else if (key === "false-value") {
            el._falseValue = nextValue;
        }
        patchAttr(el, key, nextValue, isSVG);
    }
};
function shouldSetAsProp(el, key, value, isSVG) {
    if (isSVG) {
        if (key === "innerHTML" || key === "textContent") {
            return true;
        }
        if (key in el && isNativeOn(key) && isFunction(value)) {
            return true;
        }
        return false;
    }
    if (key === "spellcheck" || key === "draggable" || key === "translate") {
        return false;
    }
    if (key === "form") {
        return false;
    }
    if (key === "list" && el.tagName === "INPUT") {
        return false;
    }
    if (key === "type" && el.tagName === "TEXTAREA") {
        return false;
    }
    if (key === "width" || key === "height") {
        const tag = el.tagName;
        if (
            tag === "IMG" ||
            tag === "VIDEO" ||
            tag === "CANVAS" ||
            tag === "SOURCE"
        ) {
            return false;
        }
    }
    if (isNativeOn(key) && isString(value)) {
        return false;
    }
    return key in el;
}
const getModelAssigner = (vnode) => {
    const fn = vnode.props["onUpdate:modelValue"] || false;
    return isArray(fn) ? (value) => invokeArrayFns(fn, value) : fn;
};
const assignKey = Symbol("_assign");
const vModelSelect = {
    // <select multiple> value need to be deep traversed
    deep: true,
    created(el, { value, modifiers: { number } }, vnode) {
        const isSetModel = isSet(value);
        addEventListener(el, "change", () => {
            const selectedVal = Array.prototype.filter
                .call(el.options, (o) => o.selected)
                .map((o) =>
                    number ? looseToNumber(getValue(o)) : getValue(o),
                );
            el[assignKey](
                el.multiple
                    ? isSetModel
                        ? new Set(selectedVal)
                        : selectedVal
                    : selectedVal[0],
            );
            el._assigning = true;
            nextTick(() => {
                el._assigning = false;
            });
        });
        el[assignKey] = getModelAssigner(vnode);
    },
    // set value in mounted & updated because <select> relies on its children
    // <option>s.
    mounted(el, { value }) {
        setSelected(el, value);
    },
    beforeUpdate(el, _binding, vnode) {
        el[assignKey] = getModelAssigner(vnode);
    },
    updated(el, { value }) {
        if (!el._assigning) {
            setSelected(el, value);
        }
    },
};
function setSelected(el, value) {
    const isMultiple = el.multiple;
    const isArrayValue = isArray(value);
    if (isMultiple && !isArrayValue && !isSet(value)) {
        return;
    }
    for (let i = 0, l = el.options.length; i < l; i++) {
        const option = el.options[i];
        const optionValue = getValue(option);
        if (isMultiple) {
            if (isArrayValue) {
                const optionType = typeof optionValue;
                if (optionType === "string" || optionType === "number") {
                    option.selected = value.some(
                        (v) => String(v) === String(optionValue),
                    );
                } else {
                    option.selected = looseIndexOf(value, optionValue) > -1;
                }
            } else {
                option.selected = value.has(optionValue);
            }
        } else if (looseEqual(getValue(option), value)) {
            if (el.selectedIndex !== i) el.selectedIndex = i;
            return;
        }
    }
    if (!isMultiple && el.selectedIndex !== -1) {
        el.selectedIndex = -1;
    }
}
function getValue(el) {
    return "_value" in el ? el._value : el.value;
}
const rendererOptions = /* @__PURE__ */ extend({ patchProp }, nodeOps);
let renderer;
function ensureRenderer() {
    return renderer || (renderer = createRenderer(rendererOptions));
}
const createApp = (...args) => {
    const app = ensureRenderer().createApp(...args);
    const { mount } = app;
    app.mount = (containerOrSelector) => {
        const container = normalizeContainer(containerOrSelector);
        if (!container) return;
        const component = app._component;
        if (
            !isFunction(component) &&
            !component.render &&
            !component.template
        ) {
            component.template = container.innerHTML;
        }
        if (container.nodeType === 1) {
            container.textContent = "";
        }
        const proxy = mount(container, false, resolveRootNamespace(container));
        if (container instanceof Element) {
            container.removeAttribute("v-cloak");
            container.setAttribute("data-v-app", "");
        }
        return proxy;
    };
    return app;
};
function resolveRootNamespace(container) {
    if (container instanceof SVGElement) {
        return "svg";
    }
    if (
        typeof MathMLElement === "function" &&
        container instanceof MathMLElement
    ) {
        return "mathml";
    }
}
function normalizeContainer(container) {
    if (isString(container)) {
        const res = document.querySelector(container);
        return res;
    }
    return container;
}
/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */
let activePinia;
const setActivePinia = (pinia2) => (activePinia = pinia2);
const piniaSymbol =
    /* istanbul ignore next */
    Symbol();
function isPlainObject(o) {
    return (
        o &&
        typeof o === "object" &&
        Object.prototype.toString.call(o) === "[object Object]" &&
        typeof o.toJSON !== "function"
    );
}
var MutationType;
(function (MutationType2) {
    MutationType2["direct"] = "direct";
    MutationType2["patchObject"] = "patch object";
    MutationType2["patchFunction"] = "patch function";
})(MutationType || (MutationType = {}));
function createPinia() {
    const scope = effectScope(true);
    const state = scope.run(() => ref({}));
    let _p = [];
    let toBeInstalled = [];
    const pinia2 = markRaw({
        install(app) {
            setActivePinia(pinia2);
            {
                pinia2._a = app;
                app.provide(piniaSymbol, pinia2);
                app.config.globalProperties.$pinia = pinia2;
                toBeInstalled.forEach((plugin) => _p.push(plugin));
                toBeInstalled = [];
            }
        },
        use(plugin) {
            if (!this._a && true) {
                toBeInstalled.push(plugin);
            } else {
                _p.push(plugin);
            }
            return this;
        },
        _p,
        // it's actually undefined here
        // @ts-expect-error
        _a: null,
        _e: scope,
        _s: /* @__PURE__ */ new Map(),
        state,
    });
    return pinia2;
}
const noop = () => {};
function addSubscription(subscriptions, callback, detached, onCleanup = noop) {
    subscriptions.push(callback);
    const removeSubscription = () => {
        const idx = subscriptions.indexOf(callback);
        if (idx > -1) {
            subscriptions.splice(idx, 1);
            onCleanup();
        }
    };
    if (!detached && getCurrentScope()) {
        onScopeDispose(removeSubscription);
    }
    return removeSubscription;
}
function triggerSubscriptions(subscriptions, ...args) {
    subscriptions.slice().forEach((callback) => {
        callback(...args);
    });
}
const fallbackRunWithContext = (fn) => fn();
const ACTION_MARKER = Symbol();
const ACTION_NAME = Symbol();
function mergeReactiveObjects(target, patchToApply) {
    if (target instanceof Map && patchToApply instanceof Map) {
        patchToApply.forEach((value, key) => target.set(key, value));
    } else if (target instanceof Set && patchToApply instanceof Set) {
        patchToApply.forEach(target.add, target);
    }
    for (const key in patchToApply) {
        if (!patchToApply.hasOwnProperty(key)) continue;
        const subPatch = patchToApply[key];
        const targetValue = target[key];
        if (
            isPlainObject(targetValue) &&
            isPlainObject(subPatch) &&
            target.hasOwnProperty(key) &&
            !isRef(subPatch) &&
            !isReactive(subPatch)
        ) {
            target[key] = mergeReactiveObjects(targetValue, subPatch);
        } else {
            target[key] = subPatch;
        }
    }
    return target;
}
const skipHydrateSymbol =
    /* istanbul ignore next */
    Symbol();
function shouldHydrate(obj) {
    return !isPlainObject(obj) || !obj.hasOwnProperty(skipHydrateSymbol);
}
const { assign } = Object;
function isComputed(o) {
    return !!(isRef(o) && o.effect);
}
function createOptionsStore(id, options, pinia2, hot) {
    const { state, actions, getters } = options;
    const initialState = pinia2.state.value[id];
    let store;
    function setup() {
        if (!initialState && true) {
            {
                pinia2.state.value[id] = state ? state() : {};
            }
        }
        const localState = toRefs(pinia2.state.value[id]);
        return assign(
            localState,
            actions,
            Object.keys(getters || {}).reduce((computedGetters, name) => {
                computedGetters[name] = markRaw(
                    computed(() => {
                        setActivePinia(pinia2);
                        const store2 = pinia2._s.get(id);
                        return getters[name].call(store2, store2);
                    }),
                );
                return computedGetters;
            }, {}),
        );
    }
    store = createSetupStore(id, setup, options, pinia2, hot, true);
    return store;
}
function createSetupStore(
    $id,
    setup,
    options = {},
    pinia2,
    hot,
    isOptionsStore,
) {
    let scope;
    const optionsForPlugin = assign({ actions: {} }, options);
    const $subscribeOptions = { deep: true };
    let isListening;
    let isSyncListening;
    let subscriptions = [];
    let actionSubscriptions = [];
    let debuggerEvents;
    const initialState = pinia2.state.value[$id];
    if (!isOptionsStore && !initialState && true) {
        {
            pinia2.state.value[$id] = {};
        }
    }
    ref({});
    let activeListener;
    function $patch(partialStateOrMutator) {
        let subscriptionMutation;
        isListening = isSyncListening = false;
        if (typeof partialStateOrMutator === "function") {
            partialStateOrMutator(pinia2.state.value[$id]);
            subscriptionMutation = {
                type: MutationType.patchFunction,
                storeId: $id,
                events: debuggerEvents,
            };
        } else {
            mergeReactiveObjects(
                pinia2.state.value[$id],
                partialStateOrMutator,
            );
            subscriptionMutation = {
                type: MutationType.patchObject,
                payload: partialStateOrMutator,
                storeId: $id,
                events: debuggerEvents,
            };
        }
        const myListenerId = (activeListener = Symbol());
        nextTick().then(() => {
            if (activeListener === myListenerId) {
                isListening = true;
            }
        });
        isSyncListening = true;
        triggerSubscriptions(
            subscriptions,
            subscriptionMutation,
            pinia2.state.value[$id],
        );
    }
    const $reset = isOptionsStore
        ? function $reset2() {
              const { state } = options;
              const newState = state ? state() : {};
              this.$patch(($state) => {
                  assign($state, newState);
              });
          }
        : /* istanbul ignore next */
          noop;
    function $dispose() {
        scope.stop();
        subscriptions = [];
        actionSubscriptions = [];
        pinia2._s.delete($id);
    }
    const action = (fn, name = "") => {
        if (ACTION_MARKER in fn) {
            fn[ACTION_NAME] = name;
            return fn;
        }
        const wrappedAction = function () {
            setActivePinia(pinia2);
            const args = Array.from(arguments);
            const afterCallbackList = [];
            const onErrorCallbackList = [];
            function after(callback) {
                afterCallbackList.push(callback);
            }
            function onError(callback) {
                onErrorCallbackList.push(callback);
            }
            triggerSubscriptions(actionSubscriptions, {
                args,
                name: wrappedAction[ACTION_NAME],
                store,
                after,
                onError,
            });
            let ret;
            try {
                ret = fn.apply(this && this.$id === $id ? this : store, args);
            } catch (error) {
                triggerSubscriptions(onErrorCallbackList, error);
                throw error;
            }
            if (ret instanceof Promise) {
                return ret
                    .then((value) => {
                        triggerSubscriptions(afterCallbackList, value);
                        return value;
                    })
                    .catch((error) => {
                        triggerSubscriptions(onErrorCallbackList, error);
                        return Promise.reject(error);
                    });
            }
            triggerSubscriptions(afterCallbackList, ret);
            return ret;
        };
        wrappedAction[ACTION_MARKER] = true;
        wrappedAction[ACTION_NAME] = name;
        return wrappedAction;
    };
    const partialStore = {
        _p: pinia2,
        // _s: scope,
        $id,
        $onAction: addSubscription.bind(null, actionSubscriptions),
        $patch,
        $reset,
        $subscribe(callback, options2 = {}) {
            const removeSubscription = addSubscription(
                subscriptions,
                callback,
                options2.detached,
                () => stopWatcher(),
            );
            const stopWatcher = scope.run(() =>
                watch(
                    () => pinia2.state.value[$id],
                    (state) => {
                        if (
                            options2.flush === "sync"
                                ? isSyncListening
                                : isListening
                        ) {
                            callback(
                                {
                                    storeId: $id,
                                    type: MutationType.direct,
                                    events: debuggerEvents,
                                },
                                state,
                            );
                        }
                    },
                    assign({}, $subscribeOptions, options2),
                ),
            );
            return removeSubscription;
        },
        $dispose,
    };
    const store = reactive(partialStore);
    pinia2._s.set($id, store);
    const runWithContext =
        (pinia2._a && pinia2._a.runWithContext) || fallbackRunWithContext;
    const setupStore = runWithContext(() =>
        pinia2._e.run(() =>
            (scope = effectScope()).run(() => setup({ action })),
        ),
    );
    for (const key in setupStore) {
        const prop = setupStore[key];
        if ((isRef(prop) && !isComputed(prop)) || isReactive(prop)) {
            if (!isOptionsStore) {
                if (initialState && shouldHydrate(prop)) {
                    if (isRef(prop)) {
                        prop.value = initialState[key];
                    } else {
                        mergeReactiveObjects(prop, initialState[key]);
                    }
                }
                {
                    pinia2.state.value[$id][key] = prop;
                }
            }
        } else if (typeof prop === "function") {
            const actionValue = action(prop, key);
            {
                setupStore[key] = actionValue;
            }
            optionsForPlugin.actions[key] = prop;
        } else;
    }
    {
        assign(store, setupStore);
        assign(toRaw(store), setupStore);
    }
    Object.defineProperty(store, "$state", {
        get: () => pinia2.state.value[$id],
        set: (state) => {
            $patch(($state) => {
                assign($state, state);
            });
        },
    });
    pinia2._p.forEach((extender) => {
        {
            assign(
                store,
                scope.run(() =>
                    extender({
                        store,
                        app: pinia2._a,
                        pinia: pinia2,
                        options: optionsForPlugin,
                    }),
                ),
            );
        }
    });
    if (initialState && isOptionsStore && options.hydrate) {
        options.hydrate(store.$state, initialState);
    }
    isListening = true;
    isSyncListening = true;
    return store;
}
/*! #__NO_SIDE_EFFECTS__ */
// @__NO_SIDE_EFFECTS__
function defineStore(idOrOptions, setup, setupOptions) {
    let id;
    let options;
    const isSetupStore = typeof setup === "function";
    if (typeof idOrOptions === "string") {
        id = idOrOptions;
        options = isSetupStore ? setupOptions : setup;
    } else {
        options = idOrOptions;
        id = idOrOptions.id;
    }
    function useStore(pinia2, hot) {
        const hasContext = hasInjectionContext();
        pinia2 = // in test mode, ignore the argument provided as we can always retrieve a
            // pinia instance with getActivePinia()
            pinia2 || (hasContext ? inject(piniaSymbol, null) : null);
        if (pinia2) setActivePinia(pinia2);
        pinia2 = activePinia;
        if (!pinia2._s.has(id)) {
            if (isSetupStore) {
                createSetupStore(id, setup, options, pinia2);
            } else {
                createOptionsStore(id, options, pinia2);
            }
        }
        const store = pinia2._s.get(id);
        return store;
    }
    useStore.$id = id;
    return useStore;
}
const suspectProtoRx =
    /"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/;
const suspectConstructorRx =
    /"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;
const JsonSigRx = /^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;
function jsonParseTransform(key, value) {
    if (
        key === "__proto__" ||
        (key === "constructor" &&
            value &&
            typeof value === "object" &&
            "prototype" in value)
    ) {
        warnKeyDropped(key);
        return;
    }
    return value;
}
function warnKeyDropped(key) {
    console.warn(
        `[destr] Dropping "${key}" key to prevent prototype pollution.`,
    );
}
function destr(value, options = {}) {
    if (typeof value !== "string") {
        return value;
    }
    const _value = value.trim();
    if (
        // eslint-disable-next-line unicorn/prefer-at
        value[0] === '"' &&
        value.endsWith('"') &&
        !value.includes("\\")
    ) {
        return _value.slice(1, -1);
    }
    if (_value.length <= 9) {
        const _lval = _value.toLowerCase();
        if (_lval === "true") {
            return true;
        }
        if (_lval === "false") {
            return false;
        }
        if (_lval === "undefined") {
            return void 0;
        }
        if (_lval === "null") {
            return null;
        }
        if (_lval === "nan") {
            return Number.NaN;
        }
        if (_lval === "infinity") {
            return Number.POSITIVE_INFINITY;
        }
        if (_lval === "-infinity") {
            return Number.NEGATIVE_INFINITY;
        }
    }
    if (!JsonSigRx.test(value)) {
        if (options.strict) {
            throw new SyntaxError("[destr] Invalid JSON");
        }
        return value;
    }
    try {
        if (suspectProtoRx.test(value) || suspectConstructorRx.test(value)) {
            if (options.strict) {
                throw new Error("[destr] Possible prototype pollution");
            }
            return JSON.parse(value, jsonParseTransform);
        }
        return JSON.parse(value);
    } catch (error) {
        if (options.strict) {
            throw error;
        }
        return value;
    }
}
function get(obj, path) {
    if (obj == null) return void 0;
    let value = obj;
    for (let i = 0; i < path.length; i++) {
        if (value == null || value[path[i]] == null) return void 0;
        value = value[path[i]];
    }
    return value;
}
function set(obj, value, path) {
    if (path.length === 0) return value;
    const idx = path[0];
    if (path.length > 1) {
        value = set(
            typeof obj !== "object" ||
                obj === null ||
                !Object.prototype.hasOwnProperty.call(obj, idx)
                ? Number.isInteger(Number(path[1]))
                    ? []
                    : {}
                : obj[idx],
            value,
            Array.prototype.slice.call(path, 1),
        );
    }
    if (Number.isInteger(Number(idx)) && Array.isArray(obj))
        return obj.slice()[idx];
    return Object.assign({}, obj, { [idx]: value });
}
function unset(obj, path) {
    if (obj == null || path.length === 0) return obj;
    if (path.length === 1) {
        if (obj == null) return obj;
        if (Number.isInteger(path[0]) && Array.isArray(obj))
            return Array.prototype.slice.call(obj, 0).splice(path[0], 1);
        const result = {};
        for (const p2 in obj) result[p2] = obj[p2];
        delete result[path[0]];
        return result;
    }
    if (obj[path[0]] == null) {
        if (Number.isInteger(path[0]) && Array.isArray(obj))
            return Array.prototype.concat.call([], obj);
        const result = {};
        for (const p2 in obj) result[p2] = obj[p2];
        return result;
    }
    return set(obj, unset(obj[path[0]], Array.prototype.slice.call(path, 1)), [
        path[0],
    ]);
}
function deepPickUnsafe(obj, paths) {
    return paths
        .map((p2) => p2.split("."))
        .map((p2) => [p2, get(obj, p2)])
        .filter((t) => t[1] !== void 0)
        .reduce((acc, cur) => set(acc, cur[1], cur[0]), {});
}
function deepOmitUnsafe(obj, paths) {
    return paths
        .map((p2) => p2.split("."))
        .reduce((acc, cur) => unset(acc, cur), obj);
}
function hydrateStore(
    store,
    {
        storage,
        serializer,
        key,
        debug,
        pick,
        omit,
        beforeHydrate,
        afterHydrate,
    },
    context,
    runHooks = true,
) {
    try {
        if (runHooks) beforeHydrate == null ? void 0 : beforeHydrate(context);
        const fromStorage = storage.getItem(key);
        if (fromStorage) {
            const deserialized = serializer.deserialize(fromStorage);
            const picked = pick
                ? deepPickUnsafe(deserialized, pick)
                : deserialized;
            const omitted = omit ? deepOmitUnsafe(picked, omit) : picked;
            store.$patch(omitted);
        }
        if (runHooks) afterHydrate == null ? void 0 : afterHydrate(context);
    } catch (error) {
        if (debug) console.error("[pinia-plugin-persistedstate]", error);
    }
}
function persistState(state, { storage, serializer, key, debug, pick, omit }) {
    try {
        const picked = pick ? deepPickUnsafe(state, pick) : state;
        const omitted = omit ? deepOmitUnsafe(picked, omit) : picked;
        const toStorage = serializer.serialize(omitted);
        storage.setItem(key, toStorage);
    } catch (error) {
        if (debug) console.error("[pinia-plugin-persistedstate]", error);
    }
}
function createPersistence(context, optionsParser, auto) {
    const {
        pinia: pinia2,
        store,
        options: { persist = auto },
    } = context;
    if (!persist) return;
    if (!(store.$id in pinia2.state.value)) {
        const originalStore = pinia2._s.get(store.$id.replace("__hot:", ""));
        if (originalStore)
            Promise.resolve().then(() => originalStore.$persist());
        return;
    }
    const persistenceOptions = Array.isArray(persist)
        ? persist
        : persist === true
          ? [{}]
          : [persist];
    const persistences = persistenceOptions.map(optionsParser);
    store.$hydrate = ({ runHooks = true } = {}) => {
        persistences.forEach((p2) => {
            hydrateStore(store, p2, context, runHooks);
        });
    };
    store.$persist = () => {
        persistences.forEach((p2) => {
            persistState(store.$state, p2);
        });
    };
    persistences.forEach((p2) => {
        hydrateStore(store, p2, context);
        store.$subscribe((_mutation, state) => persistState(state, p2), {
            detached: true,
        });
    });
}
function createPersistedState(options = {}) {
    return function (context) {
        createPersistence(
            context,
            (p2) => ({
                key: (options.key ? options.key : (x) => x)(
                    p2.key ?? context.store.$id,
                ),
                debug: p2.debug ?? options.debug ?? false,
                serializer: p2.serializer ??
                    options.serializer ?? {
                        serialize: (data) => JSON.stringify(data),
                        deserialize: (data) => destr(data),
                    },
                storage: p2.storage ?? options.storage ?? window.localStorage,
                beforeHydrate: p2.beforeHydrate,
                afterHydrate: p2.afterHydrate,
                pick: p2.pick,
                omit: p2.omit,
            }),
            options.auto ?? false,
        );
    };
}
var src_default = createPersistedState();
var __assign = function () {
    __assign =
        Object.assign ||
        function __assign2(t) {
            for (var s, i = 1, n = arguments.length; i < n; i++) {
                s = arguments[i];
                for (var p2 in s)
                    if (Object.prototype.hasOwnProperty.call(s, p2))
                        t[p2] = s[p2];
            }
            return t;
        };
    return __assign.apply(this, arguments);
};
function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P
            ? value
            : new P(function (resolve) {
                  resolve(value);
              });
    }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done
                ? resolve(result.value)
                : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function __generator(thisArg, body) {
    var _ = {
            label: 0,
            sent: function () {
                if (t[0] & 1) throw t[1];
                return t[1];
            },
            trys: [],
            ops: [],
        },
        f,
        y,
        t,
        g = Object.create(
            (typeof Iterator === "function" ? Iterator : Object).prototype,
        );
    return (
        (g.next = verb(0)),
        (g["throw"] = verb(1)),
        (g["return"] = verb(2)),
        typeof Symbol === "function" &&
            (g[Symbol.iterator] = function () {
                return this;
            }),
        g
    );
    function verb(n) {
        return function (v) {
            return step([n, v]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while ((g && ((g = 0), op[0] && (_ = 0)), _))
            try {
                if (
                    ((f = 1),
                    y &&
                        (t =
                            op[0] & 2
                                ? y["return"]
                                : op[0]
                                  ? y["throw"] ||
                                    ((t = y["return"]) && t.call(y), 0)
                                  : y.next) &&
                        !(t = t.call(y, op[1])).done)
                )
                    return t;
                if (((y = 0), t)) op = [op[0] & 2, t.value];
                switch (op[0]) {
                    case 0:
                    case 1:
                        t = op;
                        break;
                    case 4:
                        _.label++;
                        return { value: op[1], done: false };
                    case 5:
                        _.label++;
                        y = op[1];
                        op = [0];
                        continue;
                    case 7:
                        op = _.ops.pop();
                        _.trys.pop();
                        continue;
                    default:
                        if (
                            !((t = _.trys),
                            (t = t.length > 0 && t[t.length - 1])) &&
                            (op[0] === 6 || op[0] === 2)
                        ) {
                            _ = 0;
                            continue;
                        }
                        if (
                            op[0] === 3 &&
                            (!t || (op[1] > t[0] && op[1] < t[3]))
                        ) {
                            _.label = op[1];
                            break;
                        }
                        if (op[0] === 6 && _.label < t[1]) {
                            _.label = t[1];
                            t = op;
                            break;
                        }
                        if (t && _.label < t[2]) {
                            _.label = t[2];
                            _.ops.push(op);
                            break;
                        }
                        if (t[2]) _.ops.pop();
                        _.trys.pop();
                        continue;
                }
                op = body.call(thisArg, _);
            } catch (e) {
                op = [6, e];
                y = 0;
            } finally {
                f = t = 0;
            }
        if (op[0] & 5) throw op[1];
        return { value: op[0] ? op[1] : void 0, done: true };
    }
}
function __spreadArray(to, from, pack) {
    if (pack || arguments.length === 2)
        for (var i = 0, l = from.length, ar; i < l; i++) {
            if (ar || !(i in from)) {
                if (!ar) ar = Array.prototype.slice.call(from, 0, i);
                ar[i] = from[i];
            }
        }
    return to.concat(ar || Array.prototype.slice.call(from));
}
typeof SuppressedError === "function"
    ? SuppressedError
    : function (error, suppressed, message) {
          var e = new Error(message);
          return (
              (e.name = "SuppressedError"),
              (e.error = error),
              (e.suppressed = suppressed),
              e
          );
      };
var version = "4.5.1";
function wait(durationMs, resolveWith) {
    return new Promise(function (resolve) {
        return setTimeout(resolve, durationMs, resolveWith);
    });
}
function releaseEventLoop() {
    return new Promise(function (resolve) {
        var channel = new MessageChannel();
        channel.port1.onmessage = function () {
            return resolve();
        };
        channel.port2.postMessage(null);
    });
}
function requestIdleCallbackIfAvailable(fallbackTimeout, deadlineTimeout) {
    if (deadlineTimeout === void 0) {
        deadlineTimeout = Infinity;
    }
    var requestIdleCallback = window.requestIdleCallback;
    if (requestIdleCallback) {
        return new Promise(function (resolve) {
            return requestIdleCallback.call(
                window,
                function () {
                    return resolve();
                },
                { timeout: deadlineTimeout },
            );
        });
    } else {
        return wait(Math.min(fallbackTimeout, deadlineTimeout));
    }
}
function isPromise(value) {
    return !!value && typeof value.then === "function";
}
function awaitIfAsync(action, callback) {
    try {
        var returnedValue = action();
        if (isPromise(returnedValue)) {
            returnedValue.then(
                function (result) {
                    return callback(true, result);
                },
                function (error) {
                    return callback(false, error);
                },
            );
        } else {
            callback(true, returnedValue);
        }
    } catch (error) {
        callback(false, error);
    }
}
function mapWithBreaks(items, callback, loopReleaseInterval) {
    if (loopReleaseInterval === void 0) {
        loopReleaseInterval = 16;
    }
    return __awaiter(this, void 0, void 0, function () {
        var results, lastLoopReleaseTime, i, now;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    results = Array(items.length);
                    lastLoopReleaseTime = Date.now();
                    i = 0;
                    _a.label = 1;
                case 1:
                    if (!(i < items.length)) return [3, 4];
                    results[i] = callback(items[i], i);
                    now = Date.now();
                    if (!(now >= lastLoopReleaseTime + loopReleaseInterval))
                        return [3, 3];
                    lastLoopReleaseTime = now;
                    return [4, releaseEventLoop()];
                case 2:
                    _a.sent();
                    _a.label = 3;
                case 3:
                    ++i;
                    return [3, 1];
                case 4:
                    return [2, results];
            }
        });
    });
}
function suppressUnhandledRejectionWarning(promise) {
    promise.then(void 0, function () {
        return void 0;
    });
    return promise;
}
function includes(haystack, needle) {
    for (var i = 0, l = haystack.length; i < l; ++i) {
        if (haystack[i] === needle) {
            return true;
        }
    }
    return false;
}
function excludes(haystack, needle) {
    return !includes(haystack, needle);
}
function toInt(value) {
    return parseInt(value);
}
function toFloat(value) {
    return parseFloat(value);
}
function replaceNaN(value, replacement) {
    return typeof value === "number" && isNaN(value) ? replacement : value;
}
function countTruthy(values) {
    return values.reduce(function (sum, value) {
        return sum + (value ? 1 : 0);
    }, 0);
}
function round(value, base) {
    if (base === void 0) {
        base = 1;
    }
    if (Math.abs(base) >= 1) {
        return Math.round(value / base) * base;
    } else {
        var counterBase = 1 / base;
        return Math.round(value * counterBase) / counterBase;
    }
}
function parseSimpleCssSelector(selector) {
    var _a, _b;
    var errorMessage = "Unexpected syntax '".concat(selector, "'");
    var tagMatch = /^\s*([a-z-]*)(.*)$/i.exec(selector);
    var tag = tagMatch[1] || void 0;
    var attributes = {};
    var partsRegex = /([.:#][\w-]+|\[.+?\])/gi;
    var addAttribute = function (name, value) {
        attributes[name] = attributes[name] || [];
        attributes[name].push(value);
    };
    for (;;) {
        var match = partsRegex.exec(tagMatch[2]);
        if (!match) {
            break;
        }
        var part = match[0];
        switch (part[0]) {
            case ".":
                addAttribute("class", part.slice(1));
                break;
            case "#":
                addAttribute("id", part.slice(1));
                break;
            case "[": {
                var attributeMatch =
                    /^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(
                        part,
                    );
                if (attributeMatch) {
                    addAttribute(
                        attributeMatch[1],
                        (_b =
                            (_a = attributeMatch[4]) !== null && _a !== void 0
                                ? _a
                                : attributeMatch[5]) !== null && _b !== void 0
                            ? _b
                            : "",
                    );
                } else {
                    throw new Error(errorMessage);
                }
                break;
            }
            default:
                throw new Error(errorMessage);
        }
    }
    return [tag, attributes];
}
function getUTF8Bytes(input) {
    var result = new Uint8Array(input.length);
    for (var i = 0; i < input.length; i++) {
        var charCode = input.charCodeAt(i);
        if (charCode > 127) {
            return new TextEncoder().encode(input);
        }
        result[i] = charCode;
    }
    return result;
}
function x64Add(m, n) {
    var m0 = m[0] >>> 16,
        m1 = m[0] & 65535,
        m2 = m[1] >>> 16,
        m3 = m[1] & 65535;
    var n0 = n[0] >>> 16,
        n1 = n[0] & 65535,
        n2 = n[1] >>> 16,
        n3 = n[1] & 65535;
    var o0 = 0,
        o1 = 0,
        o2 = 0,
        o3 = 0;
    o3 += m3 + n3;
    o2 += o3 >>> 16;
    o3 &= 65535;
    o2 += m2 + n2;
    o1 += o2 >>> 16;
    o2 &= 65535;
    o1 += m1 + n1;
    o0 += o1 >>> 16;
    o1 &= 65535;
    o0 += m0 + n0;
    o0 &= 65535;
    m[0] = (o0 << 16) | o1;
    m[1] = (o2 << 16) | o3;
}
function x64Multiply(m, n) {
    var m0 = m[0] >>> 16,
        m1 = m[0] & 65535,
        m2 = m[1] >>> 16,
        m3 = m[1] & 65535;
    var n0 = n[0] >>> 16,
        n1 = n[0] & 65535,
        n2 = n[1] >>> 16,
        n3 = n[1] & 65535;
    var o0 = 0,
        o1 = 0,
        o2 = 0,
        o3 = 0;
    o3 += m3 * n3;
    o2 += o3 >>> 16;
    o3 &= 65535;
    o2 += m2 * n3;
    o1 += o2 >>> 16;
    o2 &= 65535;
    o2 += m3 * n2;
    o1 += o2 >>> 16;
    o2 &= 65535;
    o1 += m1 * n3;
    o0 += o1 >>> 16;
    o1 &= 65535;
    o1 += m2 * n2;
    o0 += o1 >>> 16;
    o1 &= 65535;
    o1 += m3 * n1;
    o0 += o1 >>> 16;
    o1 &= 65535;
    o0 += m0 * n3 + m1 * n2 + m2 * n1 + m3 * n0;
    o0 &= 65535;
    m[0] = (o0 << 16) | o1;
    m[1] = (o2 << 16) | o3;
}
function x64Rotl(m, bits) {
    var m0 = m[0];
    bits %= 64;
    if (bits === 32) {
        m[0] = m[1];
        m[1] = m0;
    } else if (bits < 32) {
        m[0] = (m0 << bits) | (m[1] >>> (32 - bits));
        m[1] = (m[1] << bits) | (m0 >>> (32 - bits));
    } else {
        bits -= 32;
        m[0] = (m[1] << bits) | (m0 >>> (32 - bits));
        m[1] = (m0 << bits) | (m[1] >>> (32 - bits));
    }
}
function x64LeftShift(m, bits) {
    bits %= 64;
    if (bits === 0) {
        return;
    } else if (bits < 32) {
        m[0] = m[1] >>> (32 - bits);
        m[1] = m[1] << bits;
    } else {
        m[0] = m[1] << (bits - 32);
        m[1] = 0;
    }
}
function x64Xor(m, n) {
    m[0] ^= n[0];
    m[1] ^= n[1];
}
var F1 = [4283543511, 3981806797];
var F2 = [3301882366, 444984403];
function x64Fmix(h) {
    var shifted = [0, h[0] >>> 1];
    x64Xor(h, shifted);
    x64Multiply(h, F1);
    shifted[1] = h[0] >>> 1;
    x64Xor(h, shifted);
    x64Multiply(h, F2);
    shifted[1] = h[0] >>> 1;
    x64Xor(h, shifted);
}
var C1 = [2277735313, 289559509];
var C2 = [1291169091, 658871167];
var M$1 = [0, 5];
var N1 = [0, 1390208809];
var N2 = [0, 944331445];
function x64hash128(input, seed) {
    var key = getUTF8Bytes(input);
    seed = seed || 0;
    var length = [0, key.length];
    var remainder = length[1] % 16;
    var bytes = length[1] - remainder;
    var h1 = [0, seed];
    var h2 = [0, seed];
    var k1 = [0, 0];
    var k2 = [0, 0];
    var i;
    for (i = 0; i < bytes; i = i + 16) {
        k1[0] =
            key[i + 4] |
            (key[i + 5] << 8) |
            (key[i + 6] << 16) |
            (key[i + 7] << 24);
        k1[1] =
            key[i] |
            (key[i + 1] << 8) |
            (key[i + 2] << 16) |
            (key[i + 3] << 24);
        k2[0] =
            key[i + 12] |
            (key[i + 13] << 8) |
            (key[i + 14] << 16) |
            (key[i + 15] << 24);
        k2[1] =
            key[i + 8] |
            (key[i + 9] << 8) |
            (key[i + 10] << 16) |
            (key[i + 11] << 24);
        x64Multiply(k1, C1);
        x64Rotl(k1, 31);
        x64Multiply(k1, C2);
        x64Xor(h1, k1);
        x64Rotl(h1, 27);
        x64Add(h1, h2);
        x64Multiply(h1, M$1);
        x64Add(h1, N1);
        x64Multiply(k2, C2);
        x64Rotl(k2, 33);
        x64Multiply(k2, C1);
        x64Xor(h2, k2);
        x64Rotl(h2, 31);
        x64Add(h2, h1);
        x64Multiply(h2, M$1);
        x64Add(h2, N2);
    }
    k1[0] = 0;
    k1[1] = 0;
    k2[0] = 0;
    k2[1] = 0;
    var val = [0, 0];
    switch (remainder) {
        case 15:
            val[1] = key[i + 14];
            x64LeftShift(val, 48);
            x64Xor(k2, val);
        case 14:
            val[1] = key[i + 13];
            x64LeftShift(val, 40);
            x64Xor(k2, val);
        case 13:
            val[1] = key[i + 12];
            x64LeftShift(val, 32);
            x64Xor(k2, val);
        case 12:
            val[1] = key[i + 11];
            x64LeftShift(val, 24);
            x64Xor(k2, val);
        case 11:
            val[1] = key[i + 10];
            x64LeftShift(val, 16);
            x64Xor(k2, val);
        case 10:
            val[1] = key[i + 9];
            x64LeftShift(val, 8);
            x64Xor(k2, val);
        case 9:
            val[1] = key[i + 8];
            x64Xor(k2, val);
            x64Multiply(k2, C2);
            x64Rotl(k2, 33);
            x64Multiply(k2, C1);
            x64Xor(h2, k2);
        case 8:
            val[1] = key[i + 7];
            x64LeftShift(val, 56);
            x64Xor(k1, val);
        case 7:
            val[1] = key[i + 6];
            x64LeftShift(val, 48);
            x64Xor(k1, val);
        case 6:
            val[1] = key[i + 5];
            x64LeftShift(val, 40);
            x64Xor(k1, val);
        case 5:
            val[1] = key[i + 4];
            x64LeftShift(val, 32);
            x64Xor(k1, val);
        case 4:
            val[1] = key[i + 3];
            x64LeftShift(val, 24);
            x64Xor(k1, val);
        case 3:
            val[1] = key[i + 2];
            x64LeftShift(val, 16);
            x64Xor(k1, val);
        case 2:
            val[1] = key[i + 1];
            x64LeftShift(val, 8);
            x64Xor(k1, val);
        case 1:
            val[1] = key[i];
            x64Xor(k1, val);
            x64Multiply(k1, C1);
            x64Rotl(k1, 31);
            x64Multiply(k1, C2);
            x64Xor(h1, k1);
    }
    x64Xor(h1, length);
    x64Xor(h2, length);
    x64Add(h1, h2);
    x64Add(h2, h1);
    x64Fmix(h1);
    x64Fmix(h2);
    x64Add(h1, h2);
    x64Add(h2, h1);
    return (
        ("00000000" + (h1[0] >>> 0).toString(16)).slice(-8) +
        ("00000000" + (h1[1] >>> 0).toString(16)).slice(-8) +
        ("00000000" + (h2[0] >>> 0).toString(16)).slice(-8) +
        ("00000000" + (h2[1] >>> 0).toString(16)).slice(-8)
    );
}
function errorToObject(error) {
    var _a;
    return __assign(
        {
            name: error.name,
            message: error.message,
            stack:
                (_a = error.stack) === null || _a === void 0
                    ? void 0
                    : _a.split("\n"),
        },
        error,
    );
}
function isFunctionNative(func) {
    return /^function\s.*?\{\s*\[native code]\s*}$/.test(String(func));
}
function isFinalResultLoaded(loadResult) {
    return typeof loadResult !== "function";
}
function loadSource(source, sourceOptions) {
    var sourceLoadPromise = suppressUnhandledRejectionWarning(
        new Promise(function (resolveLoad) {
            var loadStartTime = Date.now();
            awaitIfAsync(source.bind(null, sourceOptions), function () {
                var loadArgs = [];
                for (var _i = 0; _i < arguments.length; _i++) {
                    loadArgs[_i] = arguments[_i];
                }
                var loadDuration = Date.now() - loadStartTime;
                if (!loadArgs[0]) {
                    return resolveLoad(function () {
                        return { error: loadArgs[1], duration: loadDuration };
                    });
                }
                var loadResult = loadArgs[1];
                if (isFinalResultLoaded(loadResult)) {
                    return resolveLoad(function () {
                        return { value: loadResult, duration: loadDuration };
                    });
                }
                resolveLoad(function () {
                    return new Promise(function (resolveGet) {
                        var getStartTime = Date.now();
                        awaitIfAsync(loadResult, function () {
                            var getArgs = [];
                            for (var _i2 = 0; _i2 < arguments.length; _i2++) {
                                getArgs[_i2] = arguments[_i2];
                            }
                            var duration =
                                loadDuration + Date.now() - getStartTime;
                            if (!getArgs[0]) {
                                return resolveGet({
                                    error: getArgs[1],
                                    duration,
                                });
                            }
                            resolveGet({ value: getArgs[1], duration });
                        });
                    });
                });
            });
        }),
    );
    return function getComponent() {
        return sourceLoadPromise.then(function (finalizeSource) {
            return finalizeSource();
        });
    };
}
function loadSources(
    sources2,
    sourceOptions,
    excludeSources,
    loopReleaseInterval,
) {
    var includedSources = Object.keys(sources2).filter(function (sourceKey) {
        return excludes(excludeSources, sourceKey);
    });
    var sourceGettersPromise = suppressUnhandledRejectionWarning(
        mapWithBreaks(
            includedSources,
            function (sourceKey) {
                return loadSource(sources2[sourceKey], sourceOptions);
            },
            loopReleaseInterval,
        ),
    );
    return function getComponents() {
        return __awaiter(this, void 0, void 0, function () {
            var sourceGetters,
                componentPromises,
                componentArray,
                components,
                index;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        return [4, sourceGettersPromise];
                    case 1:
                        sourceGetters = _a.sent();
                        return [
                            4,
                            mapWithBreaks(
                                sourceGetters,
                                function (sourceGetter) {
                                    return suppressUnhandledRejectionWarning(
                                        sourceGetter(),
                                    );
                                },
                                loopReleaseInterval,
                            ),
                        ];
                    case 2:
                        componentPromises = _a.sent();
                        return [
                            4,
                            Promise.all(componentPromises),
                            // Keeping the component keys order the same as the source keys order
                        ];
                    case 3:
                        componentArray = _a.sent();
                        components = {};
                        for (
                            index = 0;
                            index < includedSources.length;
                            ++index
                        ) {
                            components[includedSources[index]] =
                                componentArray[index];
                        }
                        return [2, components];
                }
            });
        });
    };
}
function isTrident() {
    var w = window;
    var n = navigator;
    return (
        countTruthy([
            "MSCSSMatrix" in w,
            "msSetImmediate" in w,
            "msIndexedDB" in w,
            "msMaxTouchPoints" in n,
            "msPointerEnabled" in n,
        ]) >= 4
    );
}
function isEdgeHTML() {
    var w = window;
    var n = navigator;
    return (
        countTruthy([
            "msWriteProfilerMark" in w,
            "MSStream" in w,
            "msLaunchUri" in n,
            "msSaveBlob" in n,
        ]) >= 3 && !isTrident()
    );
}
function isChromium() {
    var w = window;
    var n = navigator;
    return (
        countTruthy([
            "webkitPersistentStorage" in n,
            "webkitTemporaryStorage" in n,
            n.vendor.indexOf("Google") === 0,
            "webkitResolveLocalFileSystemURL" in w,
            "BatteryManager" in w,
            "webkitMediaStream" in w,
            "webkitSpeechGrammar" in w,
        ]) >= 5
    );
}
function isWebKit() {
    var w = window;
    var n = navigator;
    return (
        countTruthy([
            "ApplePayError" in w,
            "CSSPrimitiveValue" in w,
            "Counter" in w,
            n.vendor.indexOf("Apple") === 0,
            "RGBColor" in w,
            "WebKitMediaKeys" in w,
        ]) >= 4
    );
}
function isDesktopWebKit() {
    var w = window;
    var HTMLElement = w.HTMLElement,
        Document = w.Document;
    return (
        countTruthy([
            "safari" in w,
            !("ongestureend" in w),
            !("TouchEvent" in w),
            !("orientation" in w),
            HTMLElement && !("autocapitalize" in HTMLElement.prototype),
            Document && "pointerLockElement" in Document.prototype,
        ]) >= 4
    );
}
function isSafariWebKit() {
    var w = window;
    return (
        // Filters-out Chrome, Yandex, DuckDuckGo (macOS and iOS), Edge
        isFunctionNative(w.print) && // Doesn't work in Safari < 15.4
        String(w.browser) === "[object WebPageNamespace]"
    );
}
function isGecko() {
    var _a, _b;
    var w = window;
    return (
        countTruthy([
            "buildID" in navigator,
            "MozAppearance" in
                ((_b =
                    (_a = document.documentElement) === null || _a === void 0
                        ? void 0
                        : _a.style) !== null && _b !== void 0
                    ? _b
                    : {}),
            "onmozfullscreenchange" in w,
            "mozInnerScreenX" in w,
            "CSSMozDocumentRule" in w,
            "CanvasCaptureMediaStream" in w,
        ]) >= 4
    );
}
function isChromium86OrNewer() {
    var w = window;
    return (
        countTruthy([
            !("MediaSettingsRange" in w),
            "RTCEncodedAudioFrame" in w,
            "" + w.Intl === "[object Intl]",
            "" + w.Reflect === "[object Reflect]",
        ]) >= 3
    );
}
function isChromium122OrNewer() {
    var w = window;
    var URLPattern = w.URLPattern;
    return (
        countTruthy([
            "union" in Set.prototype,
            "Iterator" in w,
            URLPattern && "hasRegExpGroups" in URLPattern.prototype,
            "RGB8" in WebGLRenderingContext.prototype,
        ]) >= 3
    );
}
function isWebKit606OrNewer() {
    var w = window;
    return (
        countTruthy([
            "DOMRectList" in w,
            "RTCPeerConnectionIceEvent" in w,
            "SVGGeometryElement" in w,
            "ontransitioncancel" in w,
        ]) >= 3
    );
}
function isWebKit616OrNewer() {
    var w = window;
    var n = navigator;
    var CSS = w.CSS,
        HTMLButtonElement = w.HTMLButtonElement;
    return (
        countTruthy([
            !("getStorageUpdates" in n),
            HTMLButtonElement && "popover" in HTMLButtonElement.prototype,
            "CSSCounterStyleRule" in w,
            CSS.supports("font-size-adjust: ex-height 0.5"),
            CSS.supports("text-transform: full-width"),
        ]) >= 4
    );
}
function isIPad() {
    if (navigator.platform === "iPad") {
        return true;
    }
    var s = screen;
    var screenRatio = s.width / s.height;
    return (
        countTruthy([
            // Since iOS 13. Doesn't work in Chrome on iPadOS <15, but works in desktop mode.
            "MediaSource" in window,
            // Since iOS 12. Doesn't work in Chrome on iPadOS.
            !!Element.prototype.webkitRequestFullscreen,
            // iPhone 4S that runs iOS 9 matches this, but it is not supported
            // Doesn't work in incognito mode of Safari ≥17 with split screen because of tracking prevention
            screenRatio > 0.65 && screenRatio < 1.53,
        ]) >= 2
    );
}
function getFullscreenElement() {
    var d = document;
    return (
        d.fullscreenElement ||
        d.msFullscreenElement ||
        d.mozFullScreenElement ||
        d.webkitFullscreenElement ||
        null
    );
}
function exitFullscreen() {
    var d = document;
    return (
        d.exitFullscreen ||
        d.msExitFullscreen ||
        d.mozCancelFullScreen ||
        d.webkitExitFullscreen
    ).call(d);
}
function isAndroid() {
    var isItChromium = isChromium();
    var isItGecko = isGecko();
    var w = window;
    var n = navigator;
    var c = "connection";
    if (isItChromium) {
        return (
            countTruthy([
                !("SharedWorker" in w),
                // `typechange` is deprecated, but it's still present on Android (tested on Chrome Mobile 117)
                // Removal proposal https://bugs.chromium.org/p/chromium/issues/detail?id=699892
                // Note: this expression returns true on ChromeOS, so additional detectors are required to avoid false-positives
                n[c] && "ontypechange" in n[c],
                !("sinkId" in new Audio()),
            ]) >= 2
        );
    } else if (isItGecko) {
        return (
            countTruthy([
                "onorientationchange" in w,
                "orientation" in w,
                /android/i.test(n.appVersion),
            ]) >= 2
        );
    } else {
        return false;
    }
}
function isSamsungInternet() {
    var n = navigator;
    var w = window;
    var audioPrototype = Audio.prototype;
    var visualViewport = w.visualViewport;
    return (
        countTruthy([
            "srLatency" in audioPrototype,
            "srChannelCount" in audioPrototype,
            "devicePosture" in n,
            visualViewport && "segments" in visualViewport,
            "getTextInformation" in Image.prototype,
            // Not available in Samsung Internet 21
        ]) >= 3
    );
}
function getAudioFingerprint() {
    if (doesBrowserPerformAntifingerprinting$1()) {
        return -4;
    }
    return getUnstableAudioFingerprint();
}
function getUnstableAudioFingerprint() {
    var w = window;
    var AudioContext2 = w.OfflineAudioContext || w.webkitOfflineAudioContext;
    if (!AudioContext2) {
        return -2;
    }
    if (doesBrowserSuspendAudioContext()) {
        return -1;
    }
    var hashFromIndex = 4500;
    var hashToIndex = 5e3;
    var context = new AudioContext2(1, hashToIndex, 44100);
    var oscillator = context.createOscillator();
    oscillator.type = "triangle";
    oscillator.frequency.value = 1e4;
    var compressor = context.createDynamicsCompressor();
    compressor.threshold.value = -50;
    compressor.knee.value = 40;
    compressor.ratio.value = 12;
    compressor.attack.value = 0;
    compressor.release.value = 0.25;
    oscillator.connect(compressor);
    compressor.connect(context.destination);
    oscillator.start(0);
    var _a = startRenderingAudio(context),
        renderPromise = _a[0],
        finishRendering = _a[1];
    var fingerprintPromise = suppressUnhandledRejectionWarning(
        renderPromise.then(
            function (buffer) {
                return getHash(
                    buffer.getChannelData(0).subarray(hashFromIndex),
                );
            },
            function (error) {
                if (error.name === "timeout" || error.name === "suspended") {
                    return -3;
                }
                throw error;
            },
        ),
    );
    return function () {
        finishRendering();
        return fingerprintPromise;
    };
}
function doesBrowserSuspendAudioContext() {
    return isWebKit() && !isDesktopWebKit() && !isWebKit606OrNewer();
}
function doesBrowserPerformAntifingerprinting$1() {
    return (
        // Safari ≥17
        (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) || // Samsung Internet ≥26
        (isChromium() && isSamsungInternet() && isChromium122OrNewer())
    );
}
function startRenderingAudio(context) {
    var renderTryMaxCount = 3;
    var renderRetryDelay = 500;
    var runningMaxAwaitTime = 500;
    var runningSufficientTime = 5e3;
    var finalize = function () {
        return void 0;
    };
    var resultPromise = new Promise(function (resolve, reject) {
        var isFinalized = false;
        var renderTryCount = 0;
        var startedRunningAt = 0;
        context.oncomplete = function (event) {
            return resolve(event.renderedBuffer);
        };
        var startRunningTimeout = function () {
            setTimeout(
                function () {
                    return reject(
                        makeInnerError(
                            "timeout",
                            /* InnerErrorName.Timeout */
                        ),
                    );
                },
                Math.min(
                    runningMaxAwaitTime,
                    startedRunningAt + runningSufficientTime - Date.now(),
                ),
            );
        };
        var tryRender = function () {
            try {
                var renderingPromise = context.startRendering();
                if (isPromise(renderingPromise)) {
                    suppressUnhandledRejectionWarning(renderingPromise);
                }
                switch (context.state) {
                    case "running":
                        startedRunningAt = Date.now();
                        if (isFinalized) {
                            startRunningTimeout();
                        }
                        break;
                    case "suspended":
                        if (!document.hidden) {
                            renderTryCount++;
                        }
                        if (
                            isFinalized &&
                            renderTryCount >= renderTryMaxCount
                        ) {
                            reject(
                                makeInnerError(
                                    "suspended",
                                    /* InnerErrorName.Suspended */
                                ),
                            );
                        } else {
                            setTimeout(tryRender, renderRetryDelay);
                        }
                        break;
                }
            } catch (error) {
                reject(error);
            }
        };
        tryRender();
        finalize = function () {
            if (!isFinalized) {
                isFinalized = true;
                if (startedRunningAt > 0) {
                    startRunningTimeout();
                }
            }
        };
    });
    return [resultPromise, finalize];
}
function getHash(signal) {
    var hash = 0;
    for (var i = 0; i < signal.length; ++i) {
        hash += Math.abs(signal[i]);
    }
    return hash;
}
function makeInnerError(name) {
    var error = new Error(name);
    error.name = name;
    return error;
}
function withIframe(action, initialHtml, domPollInterval) {
    var _a, _b, _c;
    if (domPollInterval === void 0) {
        domPollInterval = 50;
    }
    return __awaiter(this, void 0, void 0, function () {
        var d, iframe;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    d = document;
                    _d.label = 1;
                case 1:
                    if (!!d.body) return [3, 3];
                    return [4, wait(domPollInterval)];
                case 2:
                    _d.sent();
                    return [3, 1];
                case 3:
                    iframe = d.createElement("iframe");
                    _d.label = 4;
                case 4:
                    _d.trys.push([4, , 10, 11]);
                    return [
                        4,
                        new Promise(function (_resolve, _reject) {
                            var isComplete = false;
                            var resolve = function () {
                                isComplete = true;
                                _resolve();
                            };
                            var reject = function (error) {
                                isComplete = true;
                                _reject(error);
                            };
                            iframe.onload = resolve;
                            iframe.onerror = reject;
                            var style = iframe.style;
                            style.setProperty("display", "block", "important");
                            style.position = "absolute";
                            style.top = "0";
                            style.left = "0";
                            style.visibility = "hidden";
                            if (initialHtml && "srcdoc" in iframe) {
                                iframe.srcdoc = initialHtml;
                            } else {
                                iframe.src = "about:blank";
                            }
                            d.body.appendChild(iframe);
                            var checkReadyState = function () {
                                var _a2, _b2;
                                if (isComplete) {
                                    return;
                                }
                                if (
                                    ((_b2 =
                                        (_a2 = iframe.contentWindow) === null ||
                                        _a2 === void 0
                                            ? void 0
                                            : _a2.document) === null ||
                                    _b2 === void 0
                                        ? void 0
                                        : _b2.readyState) === "complete"
                                ) {
                                    resolve();
                                } else {
                                    setTimeout(checkReadyState, 10);
                                }
                            };
                            checkReadyState();
                        }),
                    ];
                case 5:
                    _d.sent();
                    _d.label = 6;
                case 6:
                    if (
                        !!((_b =
                            (_a = iframe.contentWindow) === null ||
                            _a === void 0
                                ? void 0
                                : _a.document) === null || _b === void 0
                            ? void 0
                            : _b.body)
                    )
                        return [3, 8];
                    return [4, wait(domPollInterval)];
                case 7:
                    _d.sent();
                    return [3, 6];
                case 8:
                    return [4, action(iframe, iframe.contentWindow)];
                case 9:
                    return [2, _d.sent()];
                case 10:
                    (_c = iframe.parentNode) === null || _c === void 0
                        ? void 0
                        : _c.removeChild(iframe);
                    return [
                        7,
                        /*endfinally*/
                    ];
                case 11:
                    return [
                        2,
                        /*return*/
                    ];
            }
        });
    });
}
function selectorToElement(selector) {
    var _a = parseSimpleCssSelector(selector),
        tag = _a[0],
        attributes = _a[1];
    var element = document.createElement(
        tag !== null && tag !== void 0 ? tag : "div",
    );
    for (var _i = 0, _b = Object.keys(attributes); _i < _b.length; _i++) {
        var name_1 = _b[_i];
        var value = attributes[name_1].join(" ");
        if (name_1 === "style") {
            addStyleString(element.style, value);
        } else {
            element.setAttribute(name_1, value);
        }
    }
    return element;
}
function addStyleString(style, source) {
    for (var _i = 0, _a = source.split(";"); _i < _a.length; _i++) {
        var property = _a[_i];
        var match = /^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(
            property,
        );
        if (match) {
            var name_2 = match[1],
                value = match[2],
                priority = match[4];
            style.setProperty(name_2, value, priority || "");
        }
    }
}
function isAnyParentCrossOrigin() {
    var currentWindow = window;
    for (;;) {
        var parentWindow = currentWindow.parent;
        if (!parentWindow || parentWindow === currentWindow) {
            return false;
        }
        try {
            if (
                parentWindow.location.origin !== currentWindow.location.origin
            ) {
                return true;
            }
        } catch (error) {
            if (error instanceof Error && error.name === "SecurityError") {
                return true;
            }
            throw error;
        }
        currentWindow = parentWindow;
    }
}
var testString = "mmMwWLliI0O&1";
var textSize = "48px";
var baseFonts = ["monospace", "sans-serif", "serif"];
var fontList = [
    // This is android-specific font from "Roboto" family
    "sans-serif-thin",
    "ARNO PRO",
    "Agency FB",
    "Arabic Typesetting",
    "Arial Unicode MS",
    "AvantGarde Bk BT",
    "BankGothic Md BT",
    "Batang",
    "Bitstream Vera Sans Mono",
    "Calibri",
    "Century",
    "Century Gothic",
    "Clarendon",
    "EUROSTILE",
    "Franklin Gothic",
    "Futura Bk BT",
    "Futura Md BT",
    "GOTHAM",
    "Gill Sans",
    "HELV",
    "Haettenschweiler",
    "Helvetica Neue",
    "Humanst521 BT",
    "Leelawadee",
    "Letter Gothic",
    "Levenim MT",
    "Lucida Bright",
    "Lucida Sans",
    "Menlo",
    "MS Mincho",
    "MS Outlook",
    "MS Reference Specialty",
    "MS UI Gothic",
    "MT Extra",
    "MYRIAD PRO",
    "Marlett",
    "Meiryo UI",
    "Microsoft Uighur",
    "Minion Pro",
    "Monotype Corsiva",
    "PMingLiU",
    "Pristina",
    "SCRIPTINA",
    "Segoe UI Light",
    "Serifa",
    "SimHei",
    "Small Fonts",
    "Staccato222 BT",
    "TRAJAN PRO",
    "Univers CE 55 Medium",
    "Vrinda",
    "ZWAdobeF",
];
function getFonts() {
    var _this = this;
    return withIframe(function (_, _a) {
        var document2 = _a.document;
        return __awaiter(_this, void 0, void 0, function () {
            var holder,
                spansContainer,
                defaultWidth,
                defaultHeight,
                createSpan,
                createSpanWithFonts,
                initializeBaseFontsSpans,
                initializeFontsSpans,
                isFontAvailable,
                baseFontsSpans,
                fontsSpans,
                index;
            return __generator(this, function (_b) {
                holder = document2.body;
                holder.style.fontSize = textSize;
                spansContainer = document2.createElement("div");
                spansContainer.style.setProperty(
                    "visibility",
                    "hidden",
                    "important",
                );
                defaultWidth = {};
                defaultHeight = {};
                createSpan = function (fontFamily) {
                    var span = document2.createElement("span");
                    var style = span.style;
                    style.position = "absolute";
                    style.top = "0";
                    style.left = "0";
                    style.fontFamily = fontFamily;
                    span.textContent = testString;
                    spansContainer.appendChild(span);
                    return span;
                };
                createSpanWithFonts = function (fontToDetect, baseFont) {
                    return createSpan(
                        "'".concat(fontToDetect, "',").concat(baseFont),
                    );
                };
                initializeBaseFontsSpans = function () {
                    return baseFonts.map(createSpan);
                };
                initializeFontsSpans = function () {
                    var spans = {};
                    var _loop_1 = function (font2) {
                        spans[font2] = baseFonts.map(function (baseFont) {
                            return createSpanWithFonts(font2, baseFont);
                        });
                    };
                    for (
                        var _i = 0, fontList_1 = fontList;
                        _i < fontList_1.length;
                        _i++
                    ) {
                        var font = fontList_1[_i];
                        _loop_1(font);
                    }
                    return spans;
                };
                isFontAvailable = function (fontSpans) {
                    return baseFonts.some(function (baseFont, baseFontIndex) {
                        return (
                            fontSpans[baseFontIndex].offsetWidth !==
                                defaultWidth[baseFont] ||
                            fontSpans[baseFontIndex].offsetHeight !==
                                defaultHeight[baseFont]
                        );
                    });
                };
                baseFontsSpans = initializeBaseFontsSpans();
                fontsSpans = initializeFontsSpans();
                holder.appendChild(spansContainer);
                for (index = 0; index < baseFonts.length; index++) {
                    defaultWidth[baseFonts[index]] =
                        baseFontsSpans[index].offsetWidth;
                    defaultHeight[baseFonts[index]] =
                        baseFontsSpans[index].offsetHeight;
                }
                return [
                    2,
                    fontList.filter(function (font) {
                        return isFontAvailable(fontsSpans[font]);
                    }),
                ];
            });
        });
    });
}
function getPlugins() {
    var rawPlugins = navigator.plugins;
    if (!rawPlugins) {
        return void 0;
    }
    var plugins = [];
    for (var i = 0; i < rawPlugins.length; ++i) {
        var plugin = rawPlugins[i];
        if (!plugin) {
            continue;
        }
        var mimeTypes = [];
        for (var j = 0; j < plugin.length; ++j) {
            var mimeType = plugin[j];
            mimeTypes.push({
                type: mimeType.type,
                suffixes: mimeType.suffixes,
            });
        }
        plugins.push({
            name: plugin.name,
            description: plugin.description,
            mimeTypes,
        });
    }
    return plugins;
}
function getCanvasFingerprint() {
    return getUnstableCanvasFingerprint(doesBrowserPerformAntifingerprinting());
}
function getUnstableCanvasFingerprint(skipImages) {
    var _a;
    var winding = false;
    var geometry;
    var text;
    var _b = makeCanvasContext(),
        canvas = _b[0],
        context = _b[1];
    if (!isSupported(canvas, context)) {
        geometry = text = "unsupported";
    } else {
        winding = doesSupportWinding(context);
        if (skipImages) {
            geometry = text = "skipped";
        } else {
            (_a = renderImages(canvas, context)),
                (geometry = _a[0]),
                (text = _a[1]);
        }
    }
    return { winding, geometry, text };
}
function makeCanvasContext() {
    var canvas = document.createElement("canvas");
    canvas.width = 1;
    canvas.height = 1;
    return [canvas, canvas.getContext("2d")];
}
function isSupported(canvas, context) {
    return !!(context && canvas.toDataURL);
}
function doesSupportWinding(context) {
    context.rect(0, 0, 10, 10);
    context.rect(2, 2, 6, 6);
    return !context.isPointInPath(5, 5, "evenodd");
}
function renderImages(canvas, context) {
    renderTextImage(canvas, context);
    var textImage1 = canvasToString(canvas);
    var textImage2 = canvasToString(canvas);
    if (textImage1 !== textImage2) {
        return [
            "unstable",
            "unstable",
            /* ImageStatus.Unstable */
        ];
    }
    renderGeometryImage(canvas, context);
    var geometryImage = canvasToString(canvas);
    return [geometryImage, textImage1];
}
function renderTextImage(canvas, context) {
    canvas.width = 240;
    canvas.height = 60;
    context.textBaseline = "alphabetic";
    context.fillStyle = "#f60";
    context.fillRect(100, 1, 62, 20);
    context.fillStyle = "#069";
    context.font = '11pt "Times New Roman"';
    var printedText = "Cwm fjordbank gly ".concat(
        String.fromCharCode(55357, 56835),
        /* 😃 */
    );
    context.fillText(printedText, 2, 15);
    context.fillStyle = "rgba(102, 204, 0, 0.2)";
    context.font = "18pt Arial";
    context.fillText(printedText, 4, 45);
}
function renderGeometryImage(canvas, context) {
    canvas.width = 122;
    canvas.height = 110;
    context.globalCompositeOperation = "multiply";
    for (
        var _i = 0,
            _a = [
                ["#f2f", 40, 40],
                ["#2ff", 80, 40],
                ["#ff2", 60, 80],
            ];
        _i < _a.length;
        _i++
    ) {
        var _b = _a[_i],
            color = _b[0],
            x = _b[1],
            y = _b[2];
        context.fillStyle = color;
        context.beginPath();
        context.arc(x, y, 40, 0, Math.PI * 2, true);
        context.closePath();
        context.fill();
    }
    context.fillStyle = "#f9c";
    context.arc(60, 60, 60, 0, Math.PI * 2, true);
    context.arc(60, 60, 20, 0, Math.PI * 2, true);
    context.fill("evenodd");
}
function canvasToString(canvas) {
    return canvas.toDataURL();
}
function doesBrowserPerformAntifingerprinting() {
    return isWebKit() && isWebKit616OrNewer() && isSafariWebKit();
}
function getTouchSupport() {
    var n = navigator;
    var maxTouchPoints = 0;
    var touchEvent;
    if (n.maxTouchPoints !== void 0) {
        maxTouchPoints = toInt(n.maxTouchPoints);
    } else if (n.msMaxTouchPoints !== void 0) {
        maxTouchPoints = n.msMaxTouchPoints;
    }
    try {
        document.createEvent("TouchEvent");
        touchEvent = true;
    } catch (_a) {
        touchEvent = false;
    }
    var touchStart = "ontouchstart" in window;
    return {
        maxTouchPoints,
        touchEvent,
        touchStart,
    };
}
function getOsCpu() {
    return navigator.oscpu;
}
function getLanguages() {
    var n = navigator;
    var result = [];
    var language =
        n.language || n.userLanguage || n.browserLanguage || n.systemLanguage;
    if (language !== void 0) {
        result.push([language]);
    }
    if (Array.isArray(n.languages)) {
        if (!(isChromium() && isChromium86OrNewer())) {
            result.push(n.languages);
        }
    } else if (typeof n.languages === "string") {
        var languages = n.languages;
        if (languages) {
            result.push(languages.split(","));
        }
    }
    return result;
}
function getColorDepth() {
    return window.screen.colorDepth;
}
function getDeviceMemory() {
    return replaceNaN(toFloat(navigator.deviceMemory), void 0);
}
function getScreenResolution() {
    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {
        return void 0;
    }
    return getUnstableScreenResolution();
}
function getUnstableScreenResolution() {
    var s = screen;
    var parseDimension = function (value) {
        return replaceNaN(toInt(value), null);
    };
    var dimensions = [parseDimension(s.width), parseDimension(s.height)];
    dimensions.sort().reverse();
    return dimensions;
}
var screenFrameCheckInterval = 2500;
var roundingPrecision = 10;
var screenFrameBackup;
var screenFrameSizeTimeoutId;
function watchScreenFrame() {
    if (screenFrameSizeTimeoutId !== void 0) {
        return;
    }
    var checkScreenFrame = function () {
        var frameSize = getCurrentScreenFrame();
        if (isFrameSizeNull(frameSize)) {
            screenFrameSizeTimeoutId = setTimeout(
                checkScreenFrame,
                screenFrameCheckInterval,
            );
        } else {
            screenFrameBackup = frameSize;
            screenFrameSizeTimeoutId = void 0;
        }
    };
    checkScreenFrame();
}
function getUnstableScreenFrame() {
    var _this = this;
    watchScreenFrame();
    return function () {
        return __awaiter(_this, void 0, void 0, function () {
            var frameSize;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        frameSize = getCurrentScreenFrame();
                        if (!isFrameSizeNull(frameSize)) return [3, 2];
                        if (screenFrameBackup) {
                            return [
                                2,
                                __spreadArray([], screenFrameBackup, true),
                            ];
                        }
                        if (!getFullscreenElement()) return [3, 2];
                        return [4, exitFullscreen()];
                    case 1:
                        _a.sent();
                        frameSize = getCurrentScreenFrame();
                        _a.label = 2;
                    case 2:
                        if (!isFrameSizeNull(frameSize)) {
                            screenFrameBackup = frameSize;
                        }
                        return [2, frameSize];
                }
            });
        });
    };
}
function getScreenFrame() {
    var _this = this;
    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {
        return function () {
            return Promise.resolve(void 0);
        };
    }
    var screenFrameGetter = getUnstableScreenFrame();
    return function () {
        return __awaiter(_this, void 0, void 0, function () {
            var frameSize, processSize;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        return [4, screenFrameGetter()];
                    case 1:
                        frameSize = _a.sent();
                        processSize = function (sideSize) {
                            return sideSize === null
                                ? null
                                : round(sideSize, roundingPrecision);
                        };
                        return [
                            2,
                            [
                                processSize(frameSize[0]),
                                processSize(frameSize[1]),
                                processSize(frameSize[2]),
                                processSize(frameSize[3]),
                            ],
                        ];
                }
            });
        });
    };
}
function getCurrentScreenFrame() {
    var s = screen;
    return [
        replaceNaN(toFloat(s.availTop), null),
        replaceNaN(
            toFloat(s.width) -
                toFloat(s.availWidth) -
                replaceNaN(toFloat(s.availLeft), 0),
            null,
        ),
        replaceNaN(
            toFloat(s.height) -
                toFloat(s.availHeight) -
                replaceNaN(toFloat(s.availTop), 0),
            null,
        ),
        replaceNaN(toFloat(s.availLeft), null),
    ];
}
function isFrameSizeNull(frameSize) {
    for (var i = 0; i < 4; ++i) {
        if (frameSize[i]) {
            return false;
        }
    }
    return true;
}
function getHardwareConcurrency() {
    return replaceNaN(toInt(navigator.hardwareConcurrency), void 0);
}
function getTimezone() {
    var _a;
    var DateTimeFormat =
        (_a = window.Intl) === null || _a === void 0
            ? void 0
            : _a.DateTimeFormat;
    if (DateTimeFormat) {
        var timezone = new DateTimeFormat().resolvedOptions().timeZone;
        if (timezone) {
            return timezone;
        }
    }
    var offset = -getTimezoneOffset();
    return "UTC".concat(offset >= 0 ? "+" : "").concat(offset);
}
function getTimezoneOffset() {
    var currentYear = /* @__PURE__ */ new Date().getFullYear();
    return Math.max(
        // `getTimezoneOffset` returns a number as a string in some unidentified cases
        toFloat(new Date(currentYear, 0, 1).getTimezoneOffset()),
        toFloat(new Date(currentYear, 6, 1).getTimezoneOffset()),
    );
}
function getSessionStorage() {
    try {
        return !!window.sessionStorage;
    } catch (error) {
        return true;
    }
}
function getLocalStorage() {
    try {
        return !!window.localStorage;
    } catch (e) {
        return true;
    }
}
function getIndexedDB() {
    if (isTrident() || isEdgeHTML()) {
        return void 0;
    }
    try {
        return !!window.indexedDB;
    } catch (e) {
        return true;
    }
}
function getOpenDatabase() {
    return !!window.openDatabase;
}
function getCpuClass() {
    return navigator.cpuClass;
}
function getPlatform() {
    var platform = navigator.platform;
    if (platform === "MacIntel") {
        if (isWebKit() && !isDesktopWebKit()) {
            return isIPad() ? "iPad" : "iPhone";
        }
    }
    return platform;
}
function getVendor() {
    return navigator.vendor || "";
}
function getVendorFlavors() {
    var flavors = [];
    for (
        var _i = 0,
            _a = [
                // Blink and some browsers on iOS
                "chrome",
                // Safari on macOS
                "safari",
                // Chrome on iOS (checked in 85 on 13 and 87 on 14)
                "__crWeb",
                "__gCrWeb",
                // Yandex Browser on iOS, macOS and Android (checked in 21.2 on iOS 14, macOS and Android)
                "yandex",
                // Yandex Browser on iOS (checked in 21.2 on 14)
                "__yb",
                "__ybro",
                // Firefox on iOS (checked in 32 on 14)
                "__firefox__",
                // Edge on iOS (checked in 46 on 14)
                "__edgeTrackingPreventionStatistics",
                "webkit",
                // Opera Touch on iOS (checked in 2.6 on 14)
                "oprt",
                // Samsung Internet on Android (checked in 11.1)
                "samsungAr",
                // UC Browser on Android (checked in 12.10 and 13.0)
                "ucweb",
                "UCShellJava",
                // Puffin on Android (checked in 9.0)
                "puffinDevice",
                // UC on iOS and Opera on Android have no specific global variables
                // Edge for Android isn't checked
            ];
        _i < _a.length;
        _i++
    ) {
        var key = _a[_i];
        var value = window[key];
        if (value && typeof value === "object") {
            flavors.push(key);
        }
    }
    return flavors.sort();
}
function areCookiesEnabled() {
    var d = document;
    try {
        d.cookie = "cookietest=1; SameSite=Strict;";
        var result = d.cookie.indexOf("cookietest=") !== -1;
        d.cookie =
            "cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT";
        return result;
    } catch (e) {
        return false;
    }
}
function getFilters() {
    return {};
}
function getDomBlockers(_a) {
    var _b = _a === void 0 ? {} : _a,
        debug = _b.debug;
    return __awaiter(this, void 0, void 0, function () {
        var filters,
            filterNames,
            allSelectors,
            blockedSelectors,
            activeBlockers;
        var _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    if (!isApplicable()) {
                        return [2, void 0];
                    }
                    filters = getFilters();
                    filterNames = Object.keys(filters);
                    allSelectors = (_c = []).concat.apply(
                        _c,
                        filterNames.map(function (filterName) {
                            return filters[filterName];
                        }),
                    );
                    return [4, getBlockedSelectors(allSelectors)];
                case 1:
                    blockedSelectors = _d.sent();
                    if (debug) {
                        printDebug(filters, blockedSelectors);
                    }
                    activeBlockers = filterNames.filter(function (filterName) {
                        var selectors = filters[filterName];
                        var blockedCount = countTruthy(
                            selectors.map(function (selector) {
                                return blockedSelectors[selector];
                            }),
                        );
                        return blockedCount > selectors.length * 0.6;
                    });
                    activeBlockers.sort();
                    return [2, activeBlockers];
            }
        });
    });
}
function isApplicable() {
    return isWebKit() || isAndroid();
}
function getBlockedSelectors(selectors) {
    var _a;
    return __awaiter(this, void 0, void 0, function () {
        var d, root, elements, blockedSelectors, i, element, holder, i;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    d = document;
                    root = d.createElement("div");
                    elements = new Array(selectors.length);
                    blockedSelectors = {};
                    forceShow(root);
                    for (i = 0; i < selectors.length; ++i) {
                        element = selectorToElement(selectors[i]);
                        if (element.tagName === "DIALOG") {
                            element.show();
                        }
                        holder = d.createElement("div");
                        forceShow(holder);
                        holder.appendChild(element);
                        root.appendChild(holder);
                        elements[i] = element;
                    }
                    _b.label = 1;
                case 1:
                    if (!!d.body) return [3, 3];
                    return [4, wait(50)];
                case 2:
                    _b.sent();
                    return [3, 1];
                case 3:
                    d.body.appendChild(root);
                    try {
                        for (i = 0; i < selectors.length; ++i) {
                            if (!elements[i].offsetParent) {
                                blockedSelectors[selectors[i]] = true;
                            }
                        }
                    } finally {
                        (_a = root.parentNode) === null || _a === void 0
                            ? void 0
                            : _a.removeChild(root);
                    }
                    return [2, blockedSelectors];
            }
        });
    });
}
function forceShow(element) {
    element.style.setProperty("visibility", "hidden", "important");
    element.style.setProperty("display", "block", "important");
}
function printDebug(filters, blockedSelectors) {
    var message = "DOM blockers debug:\n```";
    for (var _i = 0, _a = Object.keys(filters); _i < _a.length; _i++) {
        var filterName = _a[_i];
        message += "\n".concat(filterName, ":");
        for (var _b = 0, _c = filters[filterName]; _b < _c.length; _b++) {
            var selector = _c[_b];
            message += "\n  "
                .concat(blockedSelectors[selector] ? "🚫" : "➡️", " ")
                .concat(selector);
        }
    }
    console.log("".concat(message, "\n```"));
}
function getColorGamut() {
    for (var _i = 0, _a = ["rec2020", "p3", "srgb"]; _i < _a.length; _i++) {
        var gamut = _a[_i];
        if (matchMedia("(color-gamut: ".concat(gamut, ")")).matches) {
            return gamut;
        }
    }
    return void 0;
}
function areColorsInverted() {
    if (doesMatch$5("inverted")) {
        return true;
    }
    if (doesMatch$5("none")) {
        return false;
    }
    return void 0;
}
function doesMatch$5(value) {
    return matchMedia("(inverted-colors: ".concat(value, ")")).matches;
}
function areColorsForced() {
    if (doesMatch$4("active")) {
        return true;
    }
    if (doesMatch$4("none")) {
        return false;
    }
    return void 0;
}
function doesMatch$4(value) {
    return matchMedia("(forced-colors: ".concat(value, ")")).matches;
}
var maxValueToCheck = 100;
function getMonochromeDepth() {
    if (!matchMedia("(min-monochrome: 0)").matches) {
        return void 0;
    }
    for (var i = 0; i <= maxValueToCheck; ++i) {
        if (matchMedia("(max-monochrome: ".concat(i, ")")).matches) {
            return i;
        }
    }
    throw new Error("Too high value");
}
function getContrastPreference() {
    if (doesMatch$3("no-preference")) {
        return 0;
    }
    if (doesMatch$3("high") || doesMatch$3("more")) {
        return 1;
    }
    if (doesMatch$3("low") || doesMatch$3("less")) {
        return -1;
    }
    if (doesMatch$3("forced")) {
        return 10;
    }
    return void 0;
}
function doesMatch$3(value) {
    return matchMedia("(prefers-contrast: ".concat(value, ")")).matches;
}
function isMotionReduced() {
    if (doesMatch$2("reduce")) {
        return true;
    }
    if (doesMatch$2("no-preference")) {
        return false;
    }
    return void 0;
}
function doesMatch$2(value) {
    return matchMedia("(prefers-reduced-motion: ".concat(value, ")")).matches;
}
function isTransparencyReduced() {
    if (doesMatch$1("reduce")) {
        return true;
    }
    if (doesMatch$1("no-preference")) {
        return false;
    }
    return void 0;
}
function doesMatch$1(value) {
    return matchMedia("(prefers-reduced-transparency: ".concat(value, ")"))
        .matches;
}
function isHDR() {
    if (doesMatch("high")) {
        return true;
    }
    if (doesMatch("standard")) {
        return false;
    }
    return void 0;
}
function doesMatch(value) {
    return matchMedia("(dynamic-range: ".concat(value, ")")).matches;
}
var M = Math;
var fallbackFn = function () {
    return 0;
};
function getMathFingerprint() {
    var acos = M.acos || fallbackFn;
    var acosh = M.acosh || fallbackFn;
    var asin = M.asin || fallbackFn;
    var asinh = M.asinh || fallbackFn;
    var atanh = M.atanh || fallbackFn;
    var atan = M.atan || fallbackFn;
    var sin = M.sin || fallbackFn;
    var sinh = M.sinh || fallbackFn;
    var cos = M.cos || fallbackFn;
    var cosh = M.cosh || fallbackFn;
    var tan = M.tan || fallbackFn;
    var tanh = M.tanh || fallbackFn;
    var exp = M.exp || fallbackFn;
    var expm1 = M.expm1 || fallbackFn;
    var log1p = M.log1p || fallbackFn;
    var powPI = function (value) {
        return M.pow(M.PI, value);
    };
    var acoshPf = function (value) {
        return M.log(value + M.sqrt(value * value - 1));
    };
    var asinhPf = function (value) {
        return M.log(value + M.sqrt(value * value + 1));
    };
    var atanhPf = function (value) {
        return M.log((1 + value) / (1 - value)) / 2;
    };
    var sinhPf = function (value) {
        return M.exp(value) - 1 / M.exp(value) / 2;
    };
    var coshPf = function (value) {
        return (M.exp(value) + 1 / M.exp(value)) / 2;
    };
    var expm1Pf = function (value) {
        return M.exp(value) - 1;
    };
    var tanhPf = function (value) {
        return (M.exp(2 * value) - 1) / (M.exp(2 * value) + 1);
    };
    var log1pPf = function (value) {
        return M.log(1 + value);
    };
    return {
        acos: acos(0.12312423423423424),
        acosh: acosh(1e308),
        acoshPf: acoshPf(1e154),
        asin: asin(0.12312423423423424),
        asinh: asinh(1),
        asinhPf: asinhPf(1),
        atanh: atanh(0.5),
        atanhPf: atanhPf(0.5),
        atan: atan(0.5),
        sin: sin(-1e300),
        sinh: sinh(1),
        sinhPf: sinhPf(1),
        cos: cos(10.000000000123),
        cosh: cosh(1),
        coshPf: coshPf(1),
        tan: tan(-1e300),
        tanh: tanh(1),
        tanhPf: tanhPf(1),
        exp: exp(1),
        expm1: expm1(1),
        expm1Pf: expm1Pf(1),
        log1p: log1p(10),
        log1pPf: log1pPf(10),
        powPI: powPI(-100),
    };
}
var defaultText = "mmMwWLliI0fiflO&1";
var presets = {
    /**
     * The default font. User can change it in desktop Chrome, desktop Firefox, IE 11,
     * Android Chrome (but only when the size is ≥ than the default) and Android Firefox.
     */
    default: [],
    /** OS font on macOS. User can change its size and weight. Applies after Safari restart. */
    apple: [{ font: "-apple-system-body" }],
    /** User can change it in desktop Chrome and desktop Firefox. */
    serif: [{ fontFamily: "serif" }],
    /** User can change it in desktop Chrome and desktop Firefox. */
    sans: [{ fontFamily: "sans-serif" }],
    /** User can change it in desktop Chrome and desktop Firefox. */
    mono: [{ fontFamily: "monospace" }],
    /**
     * Check the smallest allowed font size. User can change it in desktop Chrome, desktop Firefox and desktop Safari.
     * The height can be 0 in Chrome on a retina display.
     */
    min: [{ fontSize: "1px" }],
    /** Tells one OS from another in desktop Chrome. */
    system: [{ fontFamily: "system-ui" }],
};
function getFontPreferences() {
    return withNaturalFonts(function (document2, container) {
        var elements = {};
        var sizes = {};
        for (var _i = 0, _a = Object.keys(presets); _i < _a.length; _i++) {
            var key = _a[_i];
            var _b = presets[key],
                _c = _b[0],
                style = _c === void 0 ? {} : _c,
                _d = _b[1],
                text = _d === void 0 ? defaultText : _d;
            var element = document2.createElement("span");
            element.textContent = text;
            element.style.whiteSpace = "nowrap";
            for (var _e = 0, _f = Object.keys(style); _e < _f.length; _e++) {
                var name_1 = _f[_e];
                var value = style[name_1];
                if (value !== void 0) {
                    element.style[name_1] = value;
                }
            }
            elements[key] = element;
            container.append(document2.createElement("br"), element);
        }
        for (var _g = 0, _h = Object.keys(presets); _g < _h.length; _g++) {
            var key = _h[_g];
            sizes[key] = elements[key].getBoundingClientRect().width;
        }
        return sizes;
    });
}
function withNaturalFonts(action, containerWidthPx) {
    if (containerWidthPx === void 0) {
        containerWidthPx = 4e3;
    }
    return withIframe(function (_, iframeWindow) {
        var iframeDocument = iframeWindow.document;
        var iframeBody = iframeDocument.body;
        var bodyStyle = iframeBody.style;
        bodyStyle.width = "".concat(containerWidthPx, "px");
        bodyStyle.webkitTextSizeAdjust = bodyStyle.textSizeAdjust = "none";
        if (isChromium()) {
            iframeBody.style.zoom = "".concat(
                1 / iframeWindow.devicePixelRatio,
            );
        } else if (isWebKit()) {
            iframeBody.style.zoom = "reset";
        }
        var linesOfText = iframeDocument.createElement("div");
        linesOfText.textContent = __spreadArray(
            [],
            Array((containerWidthPx / 20) << 0),
            true,
        )
            .map(function () {
                return "word";
            })
            .join(" ");
        iframeBody.appendChild(linesOfText);
        return action(iframeDocument, iframeBody);
    }, '<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">');
}
function isPdfViewerEnabled() {
    return navigator.pdfViewerEnabled;
}
function getArchitecture() {
    var f = new Float32Array(1);
    var u8 = new Uint8Array(f.buffer);
    f[0] = Infinity;
    f[0] = f[0] - f[0];
    return u8[3];
}
function getApplePayState() {
    var ApplePaySession = window.ApplePaySession;
    if (
        typeof (ApplePaySession === null || ApplePaySession === void 0
            ? void 0
            : ApplePaySession.canMakePayments) !== "function"
    ) {
        return -1;
    }
    if (willPrintConsoleError()) {
        return -3;
    }
    try {
        return ApplePaySession.canMakePayments() ? 1 : 0;
    } catch (error) {
        return getStateFromError(error);
    }
}
var willPrintConsoleError = isAnyParentCrossOrigin;
function getStateFromError(error) {
    if (
        error instanceof Error &&
        error.name === "InvalidAccessError" &&
        /\bfrom\b.*\binsecure\b/i.test(error.message)
    ) {
        return -2;
    }
    throw error;
}
function getPrivateClickMeasurement() {
    var _a;
    var link = document.createElement("a");
    var sourceId =
        (_a = link.attributionSourceId) !== null && _a !== void 0
            ? _a
            : link.attributionsourceid;
    return sourceId === void 0 ? void 0 : String(sourceId);
}
var STATUS_NO_GL_CONTEXT = -1;
var STATUS_GET_PARAMETER_NOT_A_FUNCTION = -2;
var validContextParameters = /* @__PURE__ */ new Set([
    10752, 2849, 2884, 2885, 2886, 2928, 2929, 2930, 2931, 2932, 2960, 2961,
    2962, 2963, 2964, 2965, 2966, 2967, 2968, 2978, 3024, 3042, 3088, 3089,
    3106, 3107, 32773, 32777, 32777, 32823, 32824, 32936, 32937, 32938, 32939,
    32968, 32969, 32970, 32971, 3317, 33170, 3333, 3379, 3386, 33901, 33902,
    34016, 34024, 34076, 3408, 3410, 3411, 3412, 3413, 3414, 3415, 34467, 34816,
    34817, 34818, 34819, 34877, 34921, 34930, 35660, 35661, 35724, 35738, 35739,
    36003, 36004, 36005, 36347, 36348, 36349, 37440, 37441, 37443, 7936, 7937,
    7938,
    // SAMPLE_ALPHA_TO_COVERAGE (32926) and SAMPLE_COVERAGE (32928) are excluded because they trigger a console warning
    // in IE, Chrome ≤ 59 and Safari ≤ 13 and give no entropy.
]);
var validExtensionParams = /* @__PURE__ */ new Set([
    34047, 35723, 36063, 34852, 34853, 34854, 34229, 36392, 36795, 38449,
    // MAX_VIEWS_OVR
]);
var shaderTypes = ["FRAGMENT_SHADER", "VERTEX_SHADER"];
var precisionTypes = [
    "LOW_FLOAT",
    "MEDIUM_FLOAT",
    "HIGH_FLOAT",
    "LOW_INT",
    "MEDIUM_INT",
    "HIGH_INT",
];
var rendererInfoExtensionName = "WEBGL_debug_renderer_info";
var polygonModeExtensionName = "WEBGL_polygon_mode";
function getWebGlBasics(_a) {
    var _b, _c, _d, _e, _f, _g;
    var cache = _a.cache;
    var gl = getWebGLContext(cache);
    if (!gl) {
        return STATUS_NO_GL_CONTEXT;
    }
    if (!isValidParameterGetter(gl)) {
        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;
    }
    var debugExtension = shouldAvoidDebugRendererInfo()
        ? null
        : gl.getExtension(rendererInfoExtensionName);
    return {
        version:
            ((_b = gl.getParameter(gl.VERSION)) === null || _b === void 0
                ? void 0
                : _b.toString()) || "",
        vendor:
            ((_c = gl.getParameter(gl.VENDOR)) === null || _c === void 0
                ? void 0
                : _c.toString()) || "",
        vendorUnmasked: debugExtension
            ? (_d = gl.getParameter(debugExtension.UNMASKED_VENDOR_WEBGL)) ===
                  null || _d === void 0
                ? void 0
                : _d.toString()
            : "",
        renderer:
            ((_e = gl.getParameter(gl.RENDERER)) === null || _e === void 0
                ? void 0
                : _e.toString()) || "",
        rendererUnmasked: debugExtension
            ? (_f = gl.getParameter(debugExtension.UNMASKED_RENDERER_WEBGL)) ===
                  null || _f === void 0
                ? void 0
                : _f.toString()
            : "",
        shadingLanguageVersion:
            ((_g = gl.getParameter(gl.SHADING_LANGUAGE_VERSION)) === null ||
            _g === void 0
                ? void 0
                : _g.toString()) || "",
    };
}
function getWebGlExtensions(_a) {
    var cache = _a.cache;
    var gl = getWebGLContext(cache);
    if (!gl) {
        return STATUS_NO_GL_CONTEXT;
    }
    if (!isValidParameterGetter(gl)) {
        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;
    }
    var extensions = gl.getSupportedExtensions();
    var contextAttributes = gl.getContextAttributes();
    var unsupportedExtensions = [];
    var attributes = [];
    var parameters = [];
    var extensionParameters = [];
    var shaderPrecisions = [];
    if (contextAttributes) {
        for (
            var _i = 0, _b = Object.keys(contextAttributes);
            _i < _b.length;
            _i++
        ) {
            var attributeName = _b[_i];
            attributes.push(
                ""
                    .concat(attributeName, "=")
                    .concat(contextAttributes[attributeName]),
            );
        }
    }
    var constants = getConstantsFromPrototype(gl);
    for (var _c = 0, constants_1 = constants; _c < constants_1.length; _c++) {
        var constant = constants_1[_c];
        var code = gl[constant];
        parameters.push(
            ""
                .concat(constant, "=")
                .concat(code)
                .concat(
                    validContextParameters.has(code)
                        ? "=".concat(gl.getParameter(code))
                        : "",
                ),
        );
    }
    if (extensions) {
        for (
            var _d = 0, extensions_1 = extensions;
            _d < extensions_1.length;
            _d++
        ) {
            var name_1 = extensions_1[_d];
            if (
                (name_1 === rendererInfoExtensionName &&
                    shouldAvoidDebugRendererInfo()) ||
                (name_1 === polygonModeExtensionName &&
                    shouldAvoidPolygonModeExtensions())
            ) {
                continue;
            }
            var extension = gl.getExtension(name_1);
            if (!extension) {
                unsupportedExtensions.push(name_1);
                continue;
            }
            for (
                var _e = 0, _f = getConstantsFromPrototype(extension);
                _e < _f.length;
                _e++
            ) {
                var constant = _f[_e];
                var code = extension[constant];
                extensionParameters.push(
                    ""
                        .concat(constant, "=")
                        .concat(code)
                        .concat(
                            validExtensionParams.has(code)
                                ? "=".concat(gl.getParameter(code))
                                : "",
                        ),
                );
            }
        }
    }
    for (
        var _g = 0, shaderTypes_1 = shaderTypes;
        _g < shaderTypes_1.length;
        _g++
    ) {
        var shaderType = shaderTypes_1[_g];
        for (
            var _h = 0, precisionTypes_1 = precisionTypes;
            _h < precisionTypes_1.length;
            _h++
        ) {
            var precisionType = precisionTypes_1[_h];
            var shaderPrecision = getShaderPrecision(
                gl,
                shaderType,
                precisionType,
            );
            shaderPrecisions.push(
                ""
                    .concat(shaderType, ".")
                    .concat(precisionType, "=")
                    .concat(shaderPrecision.join(",")),
            );
        }
    }
    extensionParameters.sort();
    parameters.sort();
    return {
        contextAttributes: attributes,
        parameters,
        shaderPrecisions,
        extensions,
        extensionParameters,
        unsupportedExtensions,
    };
}
function getWebGLContext(cache) {
    if (cache.webgl) {
        return cache.webgl.context;
    }
    var canvas = document.createElement("canvas");
    var context;
    canvas.addEventListener("webglCreateContextError", function () {
        return (context = void 0);
    });
    for (
        var _i = 0, _a = ["webgl", "experimental-webgl"];
        _i < _a.length;
        _i++
    ) {
        var type = _a[_i];
        try {
            context = canvas.getContext(type);
        } catch (_b) {}
        if (context) {
            break;
        }
    }
    cache.webgl = { context };
    return context;
}
function getShaderPrecision(gl, shaderType, precisionType) {
    var shaderPrecision = gl.getShaderPrecisionFormat(
        gl[shaderType],
        gl[precisionType],
    );
    return shaderPrecision
        ? [
              shaderPrecision.rangeMin,
              shaderPrecision.rangeMax,
              shaderPrecision.precision,
          ]
        : [];
}
function getConstantsFromPrototype(obj) {
    var keys = Object.keys(obj.__proto__);
    return keys.filter(isConstantLike);
}
function isConstantLike(key) {
    return typeof key === "string" && !key.match(/[^A-Z0-9_x]/);
}
function shouldAvoidDebugRendererInfo() {
    return isGecko();
}
function shouldAvoidPolygonModeExtensions() {
    return isChromium() || isWebKit();
}
function isValidParameterGetter(gl) {
    return typeof gl.getParameter === "function";
}
function getAudioContextBaseLatency() {
    var _a;
    var isAllowedPlatform = isAndroid() || isWebKit();
    if (!isAllowedPlatform) {
        return -2;
    }
    if (!window.AudioContext) {
        return -1;
    }
    return (_a = new AudioContext().baseLatency) !== null && _a !== void 0
        ? _a
        : -1;
}
var sources = {
    // READ FIRST:
    // See https://github.com/fingerprintjs/fingerprintjs/blob/master/contributing.md#how-to-make-an-entropy-source
    // to learn how entropy source works and how to make your own.
    // The sources run in this exact order.
    // The asynchronous sources are at the start to run in parallel with other sources.
    fonts: getFonts,
    domBlockers: getDomBlockers,
    fontPreferences: getFontPreferences,
    audio: getAudioFingerprint,
    screenFrame: getScreenFrame,
    canvas: getCanvasFingerprint,
    osCpu: getOsCpu,
    languages: getLanguages,
    colorDepth: getColorDepth,
    deviceMemory: getDeviceMemory,
    screenResolution: getScreenResolution,
    hardwareConcurrency: getHardwareConcurrency,
    timezone: getTimezone,
    sessionStorage: getSessionStorage,
    localStorage: getLocalStorage,
    indexedDB: getIndexedDB,
    openDatabase: getOpenDatabase,
    cpuClass: getCpuClass,
    platform: getPlatform,
    plugins: getPlugins,
    touchSupport: getTouchSupport,
    vendor: getVendor,
    vendorFlavors: getVendorFlavors,
    cookiesEnabled: areCookiesEnabled,
    colorGamut: getColorGamut,
    invertedColors: areColorsInverted,
    forcedColors: areColorsForced,
    monochrome: getMonochromeDepth,
    contrast: getContrastPreference,
    reducedMotion: isMotionReduced,
    reducedTransparency: isTransparencyReduced,
    hdr: isHDR,
    math: getMathFingerprint,
    pdfViewerEnabled: isPdfViewerEnabled,
    architecture: getArchitecture,
    applePay: getApplePayState,
    privateClickMeasurement: getPrivateClickMeasurement,
    audioBaseLatency: getAudioContextBaseLatency,
    // Some sources can affect other sources (e.g. WebGL can affect canvas), so it's important to run these sources
    // after other sources.
    webGlBasics: getWebGlBasics,
    webGlExtensions: getWebGlExtensions,
};
function loadBuiltinSources(options) {
    return loadSources(sources, options, []);
}
var commentTemplate = "$ if upgrade to Pro: https://fpjs.dev/pro";
function getConfidence(components) {
    var openConfidenceScore = getOpenConfidenceScore(components);
    var proConfidenceScore = deriveProConfidenceScore(openConfidenceScore);
    return {
        score: openConfidenceScore,
        comment: commentTemplate.replace(/\$/g, "".concat(proConfidenceScore)),
    };
}
function getOpenConfidenceScore(components) {
    if (isAndroid()) {
        return 0.4;
    }
    if (isWebKit()) {
        return isDesktopWebKit() && !(isWebKit616OrNewer() && isSafariWebKit())
            ? 0.5
            : 0.3;
    }
    var platform =
        "value" in components.platform ? components.platform.value : "";
    if (/^Win/.test(platform)) {
        return 0.6;
    }
    if (/^Mac/.test(platform)) {
        return 0.5;
    }
    return 0.7;
}
function deriveProConfidenceScore(openConfidenceScore) {
    return round(0.99 + 0.01 * openConfidenceScore, 1e-4);
}
function componentsToCanonicalString(components) {
    var result = "";
    for (
        var _i = 0, _a = Object.keys(components).sort();
        _i < _a.length;
        _i++
    ) {
        var componentKey = _a[_i];
        var component = components[componentKey];
        var value =
            "error" in component ? "error" : JSON.stringify(component.value);
        result += ""
            .concat(result ? "|" : "")
            .concat(componentKey.replace(/([:|\\])/g, "\\$1"), ":")
            .concat(value);
    }
    return result;
}
function componentsToDebugString(components) {
    return JSON.stringify(
        components,
        function (_key, value) {
            if (value instanceof Error) {
                return errorToObject(value);
            }
            return value;
        },
        2,
    );
}
function hashComponents(components) {
    return x64hash128(componentsToCanonicalString(components));
}
function makeLazyGetResult(components) {
    var visitorIdCache;
    var confidence = getConfidence(components);
    return {
        get visitorId() {
            if (visitorIdCache === void 0) {
                visitorIdCache = hashComponents(this.components);
            }
            return visitorIdCache;
        },
        set visitorId(visitorId) {
            visitorIdCache = visitorId;
        },
        confidence,
        components,
        version,
    };
}
function prepareForSources(delayFallback) {
    if (delayFallback === void 0) {
        delayFallback = 50;
    }
    return requestIdleCallbackIfAvailable(delayFallback, delayFallback * 2);
}
function makeAgent(getComponents, debug) {
    var creationTime = Date.now();
    return {
        get: function (options) {
            return __awaiter(this, void 0, void 0, function () {
                var startTime, components, result;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            startTime = Date.now();
                            return [4, getComponents()];
                        case 1:
                            components = _a.sent();
                            result = makeLazyGetResult(components);
                            if (
                                debug ||
                                (options === null || options === void 0
                                    ? void 0
                                    : options.debug)
                            ) {
                                console.log(
                                    "Copy the text below to get the debug data:\n\n```\nversion: "
                                        .concat(result.version, "\nuserAgent: ")
                                        .concat(
                                            navigator.userAgent,
                                            "\ntimeBetweenLoadAndGet: ",
                                        )
                                        .concat(
                                            startTime - creationTime,
                                            "\nvisitorId: ",
                                        )
                                        .concat(
                                            result.visitorId,
                                            "\ncomponents: ",
                                        )
                                        .concat(
                                            componentsToDebugString(components),
                                            "\n```",
                                        ),
                                );
                            }
                            return [2, result];
                    }
                });
            });
        },
    };
}
function monitor() {
    if (window.__fpjs_d_m || Math.random() >= 1e-3) {
        return;
    }
    try {
        var request = new XMLHttpRequest();
        request.open(
            "get",
            "https://m1.openfpcdn.io/fingerprintjs/v".concat(
                version,
                "/npm-monitoring",
            ),
            true,
        );
        request.send();
    } catch (error) {
        console.error(error);
    }
}
function load(options) {
    var _a;
    if (options === void 0) {
        options = {};
    }
    return __awaiter(this, void 0, void 0, function () {
        var delayFallback, debug, getComponents;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    if (
                        (_a = options.monitoring) !== null && _a !== void 0
                            ? _a
                            : true
                    ) {
                        monitor();
                    }
                    (delayFallback = options.delayFallback),
                        (debug = options.debug);
                    return [4, prepareForSources(delayFallback)];
                case 1:
                    _b.sent();
                    getComponents = loadBuiltinSources({ cache: {}, debug });
                    return [2, makeAgent(getComponents, debug)];
            }
        });
    });
}
const useUserStore = /* @__PURE__ */ defineStore("ysd-user", () => {
    const userId = ref(null);
    const userUuid = ref(null);
    const init = async () => {
        const storageData = await chrome.storage.local.get(null);
        if (storageData.userId) {
            userId.value = storageData.userId;
        }
        if (storageData.userUuid) {
            userUuid.value = storageData.userUuid;
        }
        if (!userId.value) {
            try {
                userId.value = await generateUserId();
                await chrome.storage.local.set({ userId: userId.value });
            } catch (e) {}
        }
    };
    const generateUserId = async () => {
        try {
            const fp = await load();
            const result = await fp.get();
            if (result.visitorId) {
                return result.visitorId;
            }
        } catch (e) {}
        return null;
    };
    return {
        userId,
        userUuid,
        init,
    };
});
const useConfigStore = /* @__PURE__ */ defineStore("ysd-config", () => {
    const user = ref({
        language: "en",
    });
    const rating = ref({
        show: false,
    });
    const userStore = useUserStore();
    const init = async () => {
        try {
            const getConfigUrl = new URL(
                "https://antonkhoteev.com/khoteev-api/ysd/config",
            );
            if (userStore.userId)
                getConfigUrl.searchParams.append("userId", userStore.userId);
            if (userStore.userUuid)
                getConfigUrl.searchParams.append(
                    "userUuid",
                    userStore.userUuid,
                );
            const response = await fetch(getConfigUrl, { method: "GET" });
            const config = await response.json();
            if (config.user) {
                user.value = config.user;
            }
            if (config.rating) {
                rating.value = config.rating;
            }
        } catch (e) {}
    };
    return {
        user,
        rating,
        init,
    };
});
const useVideoInfoStore = /* @__PURE__ */ defineStore("ysd-video-info", () => {
    const subtitlesStore = useSubtitlesStore();
    const isInitialized = ref(false);
    const tabId = ref(null);
    const videoId = ref(null);
    const currentTime = ref(0);
    const init = async () => {
        await initCommunication();
    };
    const initCommunication = () => {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(
                { type: "SIDEBAR_GET_TAB_ID" },
                (response) => {
                    if (!response || !response.tabId) {
                        chrome.runtime.sendMessage({
                            type: "SIDEBAR_CLOSE_SIDEBAR",
                        });
                        return reject(new Error("tabId not found"));
                    }
                    tabId.value = response.tabId;
                    chrome.runtime.onMessage.addListener(
                        (message, sender, sendResponse) => {
                            if (
                                message.type ===
                                `CHANNEL_CURRENT_TIME_${tabId.value}`
                            ) {
                                currentTime.value = message.value;
                            }
                            if (
                                message.type ===
                                `CHANNEL_VIDEO_ID_${tabId.value}`
                            ) {
                                videoId.value = message.value;
                            }
                        },
                    );
                    resolve(tabId.value);
                },
            );
        });
    };
    watch(videoId, async (newValue, oldValue) => {
        if (!newValue) {
            chrome.runtime.sendMessage({ type: "SIDEBAR_CLOSE_SIDEBAR" });
        }
        if (oldValue !== newValue) {
            await subtitlesStore.reset();
        }
    });
    const jumpToTime = (time) => {
        chrome.runtime.sendMessage({
            type: "MESSAGE_TO_TAB_PROXY",
            tabId: tabId.value,
            value: {
                type: "JUMP_TO_TIME",
                value: time,
            },
        });
    };
    return {
        tabId,
        videoId,
        currentTime,
        isInitialized,
        init,
        jumpToTime,
    };
});
const useSubtitlesStore = /* @__PURE__ */ defineStore("ysd-subtitles", () => {
    const videoInfoStore = useVideoInfoStore();
    const panelStore = usePanelStore();
    const userStore = useUserStore();
    const isDownloading = ref(false);
    const videoTitle = ref(null);
    const originalLanguages = ref([]);
    const translateLanguages = ref([]);
    const subtitles = ref([]);
    const translations = ref([]);
    const isShowOriginal = ref(true);
    const isShowTranslate = ref(true);
    const isTranslateFirst = ref(false);
    const youtubeObject = ref(null);
    const useAlternativeUrl = ref(false);
    const alternativeUrl = ref(null);
    let youtubeObjectLoadCounter = 0;
    watch(
        () => youtubeObject.value,
        async (newValue, oldValue) => {
            var _a, _b, _c, _d, _e, _f;
            const captionsExist =
                ((_c =
                    (_b =
                        (_a = newValue == null ? void 0 : newValue.captions) ==
                        null
                            ? void 0
                            : _a.playerCaptionsTracklistRenderer) == null
                        ? void 0
                        : _b.captionTracks) == null
                    ? void 0
                    : _c.length) > 0;
            const isUnavailable =
                ((_d =
                    newValue == null ? void 0 : newValue.playabilityStatus) ==
                null
                    ? void 0
                    : _d.status) === "ERROR";
            if (!captionsExist || isUnavailable) {
                if (youtubeObjectLoadCounter <= 5) {
                    setTimeout(() => {
                        initYoutubeObject();
                    }, 1e3);
                }
            } else {
                if (
                    ((_e = newValue == null ? void 0 : newValue.videoDetails) ==
                    null
                        ? void 0
                        : _e.videoId) ===
                    ((_f = oldValue == null ? void 0 : oldValue.videoDetails) ==
                    null
                        ? void 0
                        : _f.videoId)
                ) {
                    return;
                }
                try {
                    await initLanguages();
                    await initSubtitles();
                    await fetchVideoTitle();
                } catch (e) {}
            }
        },
        { immediate: true },
    );
    const initLanguages = async () => {
        var _a, _b, _c, _d, _e, _f;
        const captionTracks =
            (_c =
                (_b =
                    (_a = youtubeObject.value) == null
                        ? void 0
                        : _a.captions) == null
                    ? void 0
                    : _b.playerCaptionsTracklistRenderer) == null
                ? void 0
                : _c.captionTracks;
        const translationLanguages =
            (_f =
                (_e =
                    (_d = youtubeObject.value) == null
                        ? void 0
                        : _d.captions) == null
                    ? void 0
                    : _e.playerCaptionsTracklistRenderer) == null
                ? void 0
                : _f.translationLanguages;
        if (captionTracks && captionTracks.length > 0) {
            originalLanguages.value = [];
            captionTracks.map((track2) => {
                originalLanguages.value.push({
                    code: track2.languageCode,
                    name: track2.name.simpleText,
                });
            });
            const oLang = originalLanguages.value.find(
                (language) => language.code === panelStore.originalLanguage,
            );
            if (!oLang) {
                panelStore.originalLanguage = originalLanguages.value[0].code;
            }
        }
        if (translationLanguages && translationLanguages.length > 0) {
            translateLanguages.value = [];
            translationLanguages.map((track2) => {
                translateLanguages.value.push({
                    code: track2.languageCode,
                    name: track2.languageName.simpleText,
                });
            });
            const tLang = translateLanguages.value.find(
                (language) => language.code === panelStore.translateLanguage,
            );
            if (!tLang) {
                panelStore.translateLanguage = "en";
            } else {
                panelStore.translateLanguage = tLang.code;
            }
        }
    };
    const initAlternativeUrl = async () => {
        const response = await fetch(
            `https://antonkhoteev.com/khoteev-api/ysd/video/${encodeURIComponent(videoInfoStore.videoId)}/url`,
            {
                method: "GET",
                headers: {
                    Accept: "application/json",
                },
            },
        );
        if (!response.ok) {
            return;
        }
        const data = await response.json();
        alternativeUrl.value = data == null ? void 0 : data.url;
    };
    const initSubtitles = async () => {
        try {
            await fetchSubtitles();
            await fetchTranslatedSubtitles();
        } catch (e) {}
    };
    const resetTranslations = async () => {
        translations.value = [];
    };
    const reset = async () => {
        videoTitle.value = null;
        panelStore.originalLanguage = null;
        panelStore.translateLanguage = null;
        originalLanguages.value = [];
        translateLanguages.value = [];
        subtitles.value = [];
        translations.value = [];
        youtubeObjectLoadCounter = 0;
        youtubeObject.value = null;
    };
    const mergedSubtitles = computed(() => {
        return mergeSubtitles();
    });
    const subtitlesWithTranslations = computed(() => {
        return mergeSubtitlesAndTranslations();
    });
    const activeSubtitle = computed(() => {
        return (
            mergedSubtitles.value.find((sub) => {
                return (
                    videoInfoStore.currentTime >= sub.start &&
                    videoInfoStore.currentTime <= sub.end
                );
            }) || null
        );
    });
    const initYoutubeObject = async () => {
        youtubeObjectLoadCounter++;
        let response = await fetch(
            `https://www.youtube.com/watch?v=${videoInfoStore.videoId}`,
        );
        const pageText = await response.text();
        const match = pageText.match(
            /ytInitialPlayerResponse\s*=\s*({.+?})\s*;/,
        );
        if (!match) {
            throw new Error("Unknown error");
        }
        youtubeObject.value = JSON.parse(match[1]);
    };
    const fetchVideoTitle = async () => {
        var _a, _b;
        videoTitle.value =
            ((_b =
                (_a = youtubeObject.value) == null
                    ? void 0
                    : _a.videoDetails) == null
                ? void 0
                : _b.title) ?? null;
    };
    const fetchSubtitles = async () => {
        isDownloading.value = true;
        await fetchSubtitlesDirectYoutube();
        if (subtitles.value.length === 0) {
            await initAlternativeUrl();
            useAlternativeUrl.value = true;
            await fetchSubtitlesDirectYoutube();
        }
        isDownloading.value = false;
        try {
            const actionUrl = new URL(
                "https://antonkhoteev.com/khoteev-api/ysd/actions/subs-loading",
            );
            actionUrl.searchParams.append("video", videoInfoStore.videoId);
            actionUrl.searchParams.append("olang", panelStore.originalLanguage);
            actionUrl.searchParams.append(
                "tlang",
                panelStore.translateLanguage,
            );
            actionUrl.searchParams.append("dual", panelStore.isDualSubtitles);
            if (userStore.userId)
                actionUrl.searchParams.append("userId", userStore.userId);
            if (userStore.userUuid)
                actionUrl.searchParams.append("userUuid", userStore.userUuid);
            await fetch(actionUrl, { method: "GET" });
        } catch (e) {}
    };
    const fetchSubtitlesDirectYoutube = async () => {
        var _a, _b, _c, _d;
        const captionTracks =
            (_c =
                (_b =
                    (_a = youtubeObject.value) == null
                        ? void 0
                        : _a.captions) == null
                    ? void 0
                    : _b.playerCaptionsTracklistRenderer) == null
                ? void 0
                : _c.captionTracks;
        if (!captionTracks || captionTracks.length === 0) {
            return;
        }
        let subtitleUrl;
        if (useAlternativeUrl.value) {
            subtitleUrl = new URL(alternativeUrl.value);
            subtitleUrl.searchParams.set("lang", panelStore.originalLanguage);
        } else {
            subtitleUrl =
                (_d = captionTracks.find(
                    (track2) =>
                        track2.languageCode === panelStore.originalLanguage,
                )) == null
                    ? void 0
                    : _d.baseUrl;
            if (!subtitleUrl) {
                subtitleUrl = captionTracks[0].baseUrl;
            }
        }
        const subtitleResponse = await fetch(subtitleUrl.toString());
        const subtitleXml = await subtitleResponse.text();
        if (subtitleXml.length === 0) {
            return;
        }
        subtitles.value = await parseYouTubeSubtitles(subtitleXml);
    };
    const fetchTranslatedSubtitles = async () => {
        if (panelStore.originalLanguage === panelStore.translateLanguage) {
            return;
        }
        isDownloading.value = true;
        await fetchTranslatedSubtitlesDirectYoutube();
        if (translations.value.length === 0) {
            await fetchTranslatedSubtitlesDirectYoutube();
        }
        isDownloading.value = false;
    };
    const fetchTranslatedSubtitlesDirectYoutube = async () => {
        var _a, _b, _c, _d;
        const captionTracks =
            (_c =
                (_b =
                    (_a = youtubeObject.value) == null
                        ? void 0
                        : _a.captions) == null
                    ? void 0
                    : _b.playerCaptionsTracklistRenderer) == null
                ? void 0
                : _c.captionTracks;
        if (!captionTracks || captionTracks.length === 0) {
            isDownloading.value = false;
            return;
        }
        let subtitleUrl;
        if (useAlternativeUrl.value) {
            subtitleUrl = alternativeUrl.value;
        } else {
            subtitleUrl =
                (_d = captionTracks.find(
                    (track2) =>
                        track2.languageCode === panelStore.originalLanguage,
                )) == null
                    ? void 0
                    : _d.baseUrl;
            if (!subtitleUrl) {
                subtitleUrl = captionTracks[0].baseUrl;
            }
        }
        const translationUrl = new URL(subtitleUrl);
        translationUrl.searchParams.append(
            "tlang",
            panelStore.translateLanguage,
        );
        const translationsResponse = await fetch(translationUrl.toString());
        const translationsXml = await translationsResponse.text();
        const parsed = await parseYouTubeSubtitles(translationsXml);
        for (const item of parsed) {
            translations.value.push(item);
        }
    };
    const parseYouTubeSubtitles = async (xmlText) => {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, "application/xml");
        const textNodes = Array.from(xmlDoc.getElementsByTagName("text"));
        const formatTime = (time) => {
            const hours = Math.floor(time / 3600);
            const minutes = Math.floor((time % 3600) / 60);
            const seconds = Math.floor(time % 60);
            if (hours > 0) {
                return `${String(hours)}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
            } else {
                return `${String(minutes)}:${String(seconds).padStart(2, "0")}`;
            }
        };
        return textNodes.map((node, index) => {
            const start = parseFloat(node.getAttribute("start"));
            const nextNode = textNodes[index + 1];
            const end = nextNode
                ? parseFloat(nextNode.getAttribute("start"))
                : start + parseFloat(node.getAttribute("dur") || 0);
            return {
                text:
                    node.textContent
                        .replace(/&#39;/g, "'")
                        .replace(/&quot;/g, '"')
                        .replace(/&amp;/g, "&") + " ",
                start,
                end,
                startInFormat: formatTime(start),
            };
        });
    };
    const mergeSubtitlesAndTranslations = () => {
        return subtitles.value.map((sub, i) => ({
            start: sub.start,
            startInFormat: sub.startInFormat,
            end: sub.end,
            text: `${sub.text}`,
            translate: translations.value[i]
                ? `${translations.value[i].text}`
                : "",
        }));
    };
    const mergeSubtitles = () => {
        const merged = [];
        let buffer = {
            text: "",
            translate: "",
            start: null,
            end: null,
            startInFormat: null,
            textSegments: [],
            translateSegments: [],
        };
        subtitles.value.forEach((sub, index) => {
            var _a, _b;
            const cleanText = sub.text.replace(/\s+/g, " ").trim();
            const translationText =
                ((_b =
                    (_a = translations.value[index]) == null
                        ? void 0
                        : _a.text) == null
                    ? void 0
                    : _b.replace(/\s+/g, " ").trim()) || "";
            if (buffer.text.length === 0) {
                buffer = {
                    ...sub,
                    text: cleanText,
                    translate: translationText,
                    textSegments: [
                        { text: cleanText, start: sub.start, end: sub.end },
                    ],
                    translateSegments: [
                        {
                            text: translationText,
                            start: sub.start,
                            end: sub.end,
                        },
                    ],
                };
            } else if (
                buffer.text.length + cleanText.length + 1 <=
                panelStore.blockSize
            ) {
                buffer.text += " " + cleanText;
                buffer.translate += " " + translationText;
                buffer.end = sub.end;
                buffer.textSegments.push({
                    text: cleanText,
                    start: sub.start,
                    end: sub.end,
                });
                buffer.translateSegments.push({
                    text: translationText,
                    start: sub.start,
                    end: sub.end,
                });
            } else {
                merged.push({ ...buffer });
                buffer = {
                    ...sub,
                    text: cleanText,
                    translate: translationText,
                    textSegments: [
                        { text: cleanText, start: sub.start, end: sub.end },
                    ],
                    translateSegments: [
                        {
                            text: translationText,
                            start: sub.start,
                            end: sub.end,
                        },
                    ],
                };
            }
        });
        if (buffer.text.length > 0) {
            merged.push(buffer);
        }
        return merged;
    };
    const scrollToActiveSubtitle = () => {
        const highlighted = document.querySelector(".highlighted-text");
        const parentItem =
            highlighted == null ? void 0 : highlighted.closest(".subtitle");
        if (parentItem) {
            parentItem.scrollIntoView({
                behavior: "smooth",
                block: "start",
            });
        }
    };
    const copySubtitles = async () => {
        const merged = mergedSubtitles.value;
        const { isDualSubtitles, isShowTime } = panelStore;
        const textToCopy = merged
            .map((item) => {
                var _a;
                const lines = [];
                if (isShowTime) {
                    lines.push(item.startInFormat);
                }
                const original = item.text.trim();
                const translated =
                    ((_a = item.translate) == null ? void 0 : _a.trim()) ?? "";
                const originalPart = isShowOriginal.value ? original : "";
                const translatedPart =
                    isShowTranslate.value && translated ? translated : "";
                const contentLines = isDualSubtitles
                    ? isTranslateFirst.value
                        ? [translatedPart, originalPart]
                        : [originalPart, translatedPart]
                    : [originalPart];
                lines.push(...contentLines.filter(Boolean));
                return lines.join("\n");
            })
            .join("\n\n");
        try {
            await navigator.clipboard.writeText(textToCopy);
        } catch (e) {}
        try {
            const actionUrl = new URL(
                "https://antonkhoteev.com/khoteev-api/ysd/actions/copy",
            );
            if (userStore.userId)
                actionUrl.searchParams.append("userId", userStore.userId);
            if (userStore.userUuid)
                actionUrl.searchParams.append("userUuid", userStore.userUuid);
            await fetch(actionUrl, { method: "GET" });
        } catch (e) {}
    };
    const downloadSubtitles = async () => {
        const merged = mergedSubtitles.value;
        const { isDualSubtitles, format } = panelStore;
        let content = "";
        const buildTextBlock = (original, translated) => {
            const originalPart = isShowOriginal.value ? original : "";
            const translatedPart =
                isShowTranslate.value && translated ? translated : "";
            if (isDualSubtitles) {
                if (isTranslateFirst.value) {
                    return [translatedPart, originalPart]
                        .filter(Boolean)
                        .join("\n");
                } else {
                    return [originalPart, translatedPart]
                        .filter(Boolean)
                        .join("\n");
                }
            } else {
                return translatedPart || originalPart;
            }
        };
        if (format === "SRT") {
            content = merged
                .map((item, i) => {
                    var _a;
                    const index = i + 1;
                    const start = formatTimeForSRT(item.start);
                    const end = formatTimeForSRT(item.end);
                    const original = item.text.trim();
                    const translated =
                        ((_a = item.translate) == null ? void 0 : _a.trim()) ??
                        "";
                    const textBlock = buildTextBlock(original, translated);
                    return `${index}
${start} --> ${end}
${textBlock}
`;
                })
                .join("\n");
        }
        if (format === "TXT") {
            content = merged
                .map((item) => {
                    var _a;
                    const original = item.text.trim();
                    const translated =
                        ((_a = item.translate) == null ? void 0 : _a.trim()) ??
                        "";
                    const textBlock = buildTextBlock(original, translated);
                    if (panelStore.isShowTime) {
                        const time = formatTimeForTXT(item.start);
                        return `${time}
${textBlock}`;
                    } else {
                        return `${textBlock}`;
                    }
                })
                .join("\n\n");
        }
        const rawTitle = videoTitle.value ?? "";
        const sanitizedTitle = sanitizeFileName(rawTitle);
        const fallbackName = `subtitles-${/* @__PURE__ */ new Date().toISOString().split("T")[0]}`;
        const baseFileName = sanitizedTitle || fallbackName;
        const fileName = `${baseFileName}.${format.toLowerCase()}`;
        const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        a.click();
        URL.revokeObjectURL(url);
        try {
            const actionUrl = new URL(
                "https://antonkhoteev.com/khoteev-api/ysd/actions/download",
            );
            actionUrl.searchParams.append("format", format);
            if (userStore.userId)
                actionUrl.searchParams.append("userId", userStore.userId);
            if (userStore.userUuid)
                actionUrl.searchParams.append("userUuid", userStore.userUuid);
            await fetch(actionUrl, { method: "GET" });
        } catch (e) {}
    };
    const formatTimeForSRT = (seconds) => {
        const date = new Date(seconds * 1e3);
        const hh = String(date.getUTCHours()).padStart(2, "0");
        const mm = String(date.getUTCMinutes()).padStart(2, "0");
        const ss = String(date.getUTCSeconds()).padStart(2, "0");
        const ms = String(date.getUTCMilliseconds()).padStart(3, "0");
        return `${hh}:${mm}:${ss},${ms}`;
    };
    const formatTimeForTXT = (seconds) => {
        const mins = Math.floor(seconds / 60).toString();
        const secs = Math.floor(seconds % 60)
            .toString()
            .padStart(2, "0");
        return `${mins}:${secs}`;
    };
    function sanitizeFileName(name) {
        return String(name || "")
            .replace(/[/\\?%*:|"<>]/g, "")
            .trim();
    }
    return {
        subtitles,
        translations,
        subtitlesWithTranslations,
        mergedSubtitles,
        activeSubtitle,
        isDownloading,
        originalLanguages,
        translateLanguages,
        isShowOriginal,
        isShowTranslate,
        isTranslateFirst,
        reset,
        resetTranslations,
        initYoutubeObject,
        fetchVideoTitle,
        initLanguages,
        initSubtitles,
        fetchSubtitles,
        fetchTranslatedSubtitles,
        copySubtitles,
        downloadSubtitles,
        scrollToActiveSubtitle,
    };
});
const usePanelStore = /* @__PURE__ */ defineStore(
    "ysd-panel",
    () => {
        const subtitlesStore = useSubtitlesStore();
        const videoInfoStore = useVideoInfoStore();
        const userStore = useUserStore();
        const isLightTheme = ref(false);
        watch(isLightTheme.value, (newValue) => {
            if (newValue) {
                document.documentElement.setAttribute("data-theme", "light");
            } else {
                document.documentElement.setAttribute("data-theme", "dark");
            }
        });
        const initTheme = async () => {
            if (isLightTheme.value) {
                document.documentElement.setAttribute("data-theme", "light");
            } else {
                document.documentElement.setAttribute("data-theme", "dark");
            }
        };
        const changeTheme = async () => {
            isLightTheme.value = !isLightTheme.value;
            await initTheme();
        };
        const isAutoScrollEnabled = ref(true);
        const toggleAutoScroll = () => {
            isAutoScrollEnabled.value = !isAutoScrollEnabled.value;
            if (isAutoScrollEnabled.value) {
                subtitlesStore.scrollToActiveSubtitle();
            }
        };
        const isShowTime = ref(true);
        const toggleShowTime = () => {
            isShowTime.value = !isShowTime.value;
        };
        const blockSize = ref(200);
        const blockSizeDiff = 25;
        const increaseBlockSize = async () => {
            blockSize.value += blockSizeDiff;
            try {
                const actionUrl = new URL(
                    "https://antonkhoteev.com/khoteev-api/ysd/actions/change-block",
                );
                actionUrl.searchParams.append("blockSize", blockSize.value);
                if (userStore.userId)
                    actionUrl.searchParams.append("userId", userStore.userId);
                if (userStore.userUuid)
                    actionUrl.searchParams.append(
                        "userUuid",
                        userStore.userUuid,
                    );
                await fetch(actionUrl, { method: "GET" });
            } catch (e) {}
        };
        const decreaseBlockSize = async () => {
            if (blockSize.value > blockSizeDiff) {
                blockSize.value -= blockSizeDiff;
                try {
                    const actionUrl = new URL(
                        "https://antonkhoteev.com/khoteev-api/ysd/actions/change-block",
                    );
                    actionUrl.searchParams.append("blockSize", blockSize.value);
                    if (userStore.userId)
                        actionUrl.searchParams.append(
                            "userId",
                            userStore.userId,
                        );
                    if (userStore.userUuid)
                        actionUrl.searchParams.append(
                            "userUuid",
                            userStore.userUuid,
                        );
                    await fetch(actionUrl, { method: "GET" });
                } catch (e) {}
            }
        };
        const originalLanguage = ref(null);
        const translateLanguage = ref(null);
        const isDualSubtitles = ref(true);
        const isShowTranslate = computed(() => {
            return (
                isDualSubtitles.value &&
                subtitlesStore.isDownloading === false &&
                originalLanguage.value !== translateLanguage.value
            );
        });
        const toggleShowTranslate = () => {
            isDualSubtitles.value = !isDualSubtitles.value;
        };
        watch(isDualSubtitles, async (newValue, oldValue) => {
            if (!videoInfoStore.isInitialized) return;
            if (newValue === true) {
                await subtitlesStore.fetchTranslatedSubtitles();
            }
        });
        watch(originalLanguage, async (newValue, oldValue) => {
            if (oldValue && newValue && oldValue !== newValue) {
                await subtitlesStore.fetchSubtitles();
                if (subtitlesStore.translations.length === 0) {
                    await subtitlesStore.fetchTranslatedSubtitles();
                }
            }
        });
        watch(translateLanguage, async (newValue, oldValue) => {
            if (oldValue && newValue && oldValue !== newValue) {
                await subtitlesStore.resetTranslations();
                await subtitlesStore.fetchTranslatedSubtitles();
            }
        });
        const format = ref("SRT");
        const changeFormat = () => {
            if (format.value === "SRT") {
                format.value = "TXT";
            } else {
                format.value = "SRT";
            }
        };
        return {
            isLightTheme,
            initTheme,
            changeTheme,
            isAutoScrollEnabled,
            toggleAutoScroll,
            isShowTime,
            toggleShowTime,
            isShowTranslate,
            isDualSubtitles,
            toggleShowTranslate,
            blockSize,
            increaseBlockSize,
            decreaseBlockSize,
            originalLanguage,
            translateLanguage,
            format,
            changeFormat,
        };
    },
    {
        persist: true,
    },
);
const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
        target[key] = val;
    }
    return target;
};
const _hoisted_1$3 = { class: "panel" };
const _hoisted_2$3 = { class: "line line-language" };
const _hoisted_3$3 = ["disabled"];
const _hoisted_4$3 = ["value"];
const _hoisted_5$2 = {
    key: 0,
    class: "line line-language",
};
const _hoisted_6$1 = ["disabled"];
const _hoisted_7 = ["value"];
const _hoisted_8 = { class: "line line-block-settings" };
const _hoisted_9 = { class: "buttons" };
const _hoisted_10 = ["disabled"];
const _hoisted_11 = { class: "buttons" };
const _hoisted_12 = ["disabled"];
const _hoisted_13 = ["disabled"];
const _hoisted_14 = ["disabled"];
const _hoisted_15 = ["disabled"];
const _hoisted_16 = { class: "line line-download" };
const _hoisted_17 = { class: "buttons" };
const _hoisted_18 = ["disabled"];
const _hoisted_19 = { class: "action-icon" };
const _hoisted_20 = ["disabled"];
const _hoisted_21 = ["disabled"];
const _hoisted_22 = { class: "buttons" };
const _hoisted_23 = { class: "action-icon" };
const _hoisted_24 = {
    key: 0,
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
};
const _hoisted_25 = {
    key: 1,
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
};
const _sfc_main$3 = {
    __name: "YsdPanel",
    setup(__props) {
        const panelStore = usePanelStore();
        const subtitlesStore = useSubtitlesStore();
        return (_ctx, _cache) => {
            return (
                openBlock(),
                createElementBlock("div", _hoisted_1$3, [
                    createBaseVNode("div", _hoisted_2$3, [
                        _cache[11] ||
                            (_cache[11] = createBaseVNode(
                                "div",
                                { class: "line-language-title" },
                                "Original",
                                -1,
                            )),
                        withDirectives(
                            createBaseVNode(
                                "select",
                                {
                                    class: "input language",
                                    disabled:
                                        unref(subtitlesStore).originalLanguages
                                            .length === 0,
                                    "onUpdate:modelValue":
                                        _cache[0] ||
                                        (_cache[0] = ($event) =>
                                            (unref(
                                                panelStore,
                                            ).originalLanguage = $event)),
                                },
                                [
                                    (openBlock(true),
                                    createElementBlock(
                                        Fragment,
                                        null,
                                        renderList(
                                            unref(subtitlesStore)
                                                .originalLanguages,
                                            (lang) => {
                                                return (
                                                    openBlock(),
                                                    createElementBlock(
                                                        "option",
                                                        {
                                                            key: lang.code,
                                                            value: lang.code,
                                                        },
                                                        toDisplayString(
                                                            lang.name,
                                                        ),
                                                        9,
                                                        _hoisted_4$3,
                                                    )
                                                );
                                            },
                                        ),
                                        128,
                                    )),
                                ],
                                8,
                                _hoisted_3$3,
                            ),
                            [
                                [
                                    vModelSelect,
                                    unref(panelStore).originalLanguage,
                                ],
                            ],
                        ),
                    ]),
                    unref(panelStore).isDualSubtitles
                        ? (openBlock(),
                          createElementBlock("div", _hoisted_5$2, [
                              _cache[12] ||
                                  (_cache[12] = createBaseVNode(
                                      "div",
                                      { class: "line-language-title" },
                                      "Translate",
                                      -1,
                                  )),
                              withDirectives(
                                  createBaseVNode(
                                      "select",
                                      {
                                          class: "input language",
                                          disabled:
                                              unref(subtitlesStore)
                                                  .translateLanguages.length ===
                                              0,
                                          "onUpdate:modelValue":
                                              _cache[1] ||
                                              (_cache[1] = ($event) =>
                                                  (unref(
                                                      panelStore,
                                                  ).translateLanguage =
                                                      $event)),
                                      },
                                      [
                                          (openBlock(true),
                                          createElementBlock(
                                              Fragment,
                                              null,
                                              renderList(
                                                  unref(subtitlesStore)
                                                      .translateLanguages,
                                                  (lang) => {
                                                      return (
                                                          openBlock(),
                                                          createElementBlock(
                                                              "option",
                                                              {
                                                                  key: lang.code,
                                                                  value: lang.code,
                                                              },
                                                              toDisplayString(
                                                                  lang.name,
                                                              ),
                                                              9,
                                                              _hoisted_7,
                                                          )
                                                      );
                                                  },
                                              ),
                                              128,
                                          )),
                                      ],
                                      8,
                                      _hoisted_6$1,
                                  ),
                                  [
                                      [
                                          vModelSelect,
                                          unref(panelStore).translateLanguage,
                                      ],
                                  ],
                              ),
                          ]))
                        : createCommentVNode("", true),
                    createBaseVNode("div", _hoisted_8, [
                        createBaseVNode("div", _hoisted_9, [
                            createBaseVNode(
                                "button",
                                {
                                    class: normalizeClass([
                                        "action action-autoscroll",
                                        {
                                            "action-active":
                                                unref(panelStore)
                                                    .isAutoScrollEnabled,
                                        },
                                    ]),
                                    onClick:
                                        _cache[2] ||
                                        (_cache[2] = (...args) =>
                                            unref(panelStore)
                                                .toggleAutoScroll &&
                                            unref(panelStore).toggleAutoScroll(
                                                ...args,
                                            )),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                _cache[13] ||
                                    (_cache[13] = [
                                        createStaticVNode(
                                            '<span class="action-icon" data-v-787ce242><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-787ce242><path d="M11.6548 1.59658C11.4148 1.70274 11.2164 1.88274 11.1149 2.06736C11.0688 2.15967 11.0411 2.41352 11.0227 2.86583L10.995 3.53046L10.8289 3.55815C9.58772 3.77046 8.66955 4.09354 7.71447 4.65663C5.49517 5.96281 3.99103 8.15054 3.56194 10.689L3.53425 10.8552L2.87446 10.8783C2.13623 10.906 1.96091 10.9567 1.76251 11.1967C1.39339 11.6352 1.41646 12.2629 1.81326 12.6275C2.10394 12.8998 2.16853 12.9183 2.86062 12.946L3.51118 12.9691L3.52503 13.1306C3.5481 13.4168 3.72342 14.2014 3.86646 14.6537C4.36476 16.2184 5.3383 17.6353 6.63942 18.683C7.71908 19.5553 9.02482 20.1692 10.3905 20.4461C10.6997 20.5107 10.9627 20.5615 10.9719 20.5615C10.9857 20.5615 11.0042 20.8384 11.018 21.1707C11.0411 21.8446 11.078 21.96 11.341 22.2046C11.5625 22.4169 11.7563 22.5 12.0239 22.5C12.4622 22.5 12.9005 22.1862 13.0159 21.7892C13.0574 21.6508 13.0851 21.3646 13.0851 21.0646V20.5707L13.2143 20.543C13.2835 20.5292 13.4311 20.5061 13.5465 20.4923C14.5477 20.3538 16.0887 19.703 17.0577 19.0107C17.5283 18.6738 18.3911 17.8476 18.7925 17.3538C19.7337 16.1861 20.3981 14.6399 20.5827 13.186L20.615 12.946H21.0441C21.6393 12.946 21.8931 12.886 22.1284 12.6783C22.599 12.2768 22.6267 11.6306 22.1883 11.1829C21.9669 10.9567 21.81 10.9106 21.1456 10.8737L20.5873 10.846L20.4858 10.3429C19.8768 7.44899 17.8143 5.04433 15.0598 4.0197C14.5892 3.84431 13.5649 3.57661 13.3619 3.57661C13.1451 3.57661 13.1266 3.53507 13.0897 2.87968C13.0435 2.17351 12.9882 2.00274 12.739 1.7812C12.3976 1.48581 12.0377 1.42119 11.6548 1.59658ZM11.8532 6.23512C12.3145 6.2305 12.7806 6.24435 12.8913 6.26743L13.0851 6.29974V5.95358C13.0851 5.76434 13.1035 5.60742 13.1266 5.60742C13.2696 5.60742 14.2755 5.90281 14.6031 6.04127C15.9642 6.6182 17.1546 7.70745 17.8559 9.02747C18.0589 9.40594 18.4003 10.3429 18.4603 10.6937L18.4926 10.869H17.6944L17.7313 11.1321C17.7497 11.2798 17.7682 11.746 17.7636 12.1706L17.759 12.946H18.0543C18.368 12.946 18.5295 13.0014 18.5295 13.1075C18.5295 13.1398 18.4557 13.426 18.368 13.7399C17.8559 15.5168 16.6932 16.9707 15.106 17.8153C14.7322 18.0138 13.671 18.3922 13.3112 18.4569C13.1497 18.4846 13.1404 18.4799 13.1128 18.3415C13.0989 18.263 13.0851 18.083 13.0851 17.9492V17.7045L12.9374 17.7368C12.8544 17.7553 12.3838 17.7692 11.8947 17.7692H11.0088V18.4799L10.8612 18.4569C9.58772 18.2261 8.27737 17.5107 7.34535 16.5414C6.46409 15.623 5.82737 14.4229 5.6382 13.3152L5.58283 12.9691L6.2334 12.9414V12.1244C6.2334 11.6767 6.24724 11.2106 6.2657 11.086L6.30261 10.869H5.62436L5.66127 10.6613C5.71664 10.3613 5.95656 9.66902 6.14112 9.27209C6.46871 8.5567 7.14234 7.65668 7.70985 7.17667C8.08358 6.85821 8.85872 6.34127 9.21861 6.16589C9.60618 5.98588 10.3029 5.7505 10.6812 5.68126L10.9857 5.62588L11.0134 6.23512H11.8532Z" data-v-787ce242></path><path d="M9.85956 11.7199C9.25183 13.5951 8.74963 15.1527 8.74235 15.1791C8.73143 15.2207 8.89155 15.2283 9.59391 15.2207L10.3576 15.2107C10.4165 15.2099 10.4681 15.1712 10.4855 15.1149C10.5001 15.0619 10.5983 14.7444 10.7002 14.4041L10.8211 14.0051C10.8595 13.8783 10.9763 13.7917 11.1086 13.7917H12.9693C13.1021 13.7917 13.2191 13.8789 13.2571 14.0062L13.5583 15.0137C13.5964 15.1411 13.7134 15.2283 13.8461 15.2283H14.9374C15.1416 15.2283 15.2863 15.0288 15.2231 14.8345L14.6414 13.0431C14.252 11.8447 13.7462 10.2909 13.5242 9.59525L13.1797 8.53426C13.1398 8.41146 13.0261 8.32784 12.8972 8.32648L12.043 8.31742L11.1892 8.31141C11.0583 8.31049 10.942 8.39445 10.9014 8.51899L9.85956 11.7199ZM12.4361 11.2965L12.6808 12.1055C12.7387 12.2968 12.5975 12.4904 12.3979 12.4935L12.054 12.4987C11.7094 12.5019 11.4134 12.5024 11.3222 12.4958C11.3026 12.4944 11.2936 12.4816 11.2979 12.4624C11.3221 12.355 11.4331 11.9607 11.5736 11.4969C11.9205 10.352 11.9942 10.1049 12.0266 10.0714C12.038 10.0596 12.0512 10.0819 12.0573 10.0971C12.1016 10.2068 12.2601 10.7107 12.4361 11.2965Z" data-v-787ce242></path></svg></span><span class="action-title" data-v-787ce242> auto scroll </span>',
                                            2,
                                        ),
                                    ]),
                                10,
                                _hoisted_10,
                            ),
                        ]),
                        createBaseVNode("div", _hoisted_11, [
                            createBaseVNode(
                                "button",
                                {
                                    class: normalizeClass([
                                        "action action-translation",
                                        {
                                            "action-active":
                                                unref(panelStore)
                                                    .isDualSubtitles,
                                        },
                                    ]),
                                    onClick:
                                        _cache[3] ||
                                        (_cache[3] = (...args) =>
                                            unref(panelStore)
                                                .toggleShowTranslate &&
                                            unref(
                                                panelStore,
                                            ).toggleShowTranslate(...args)),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                _cache[14] ||
                                    (_cache[14] = [
                                        createStaticVNode(
                                            '<span class="action-icon" data-v-787ce242><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-787ce242><path d="M1.92468 2.05005C1.02902 2.2223 0.237499 2.99236 0.0448263 3.88402C0.00837474 4.07147 -0.00724735 5.80411 0.00316738 9.66963L0.0181381 14.9616C0.0185685 15.1137 0.0483867 15.2644 0.106044 15.4059L0.138559 15.4856C0.414549 16.1442 1.00819 16.6914 1.65911 16.889C1.98717 16.9903 2.1486 16.9954 5.27823 17.0055L7.3306 17.0118C8.01265 17.0139 8.56739 17.5462 8.57803 18.2088L8.5849 18.6368C8.61615 20.4049 8.62135 20.4455 8.94942 20.9419C9.24624 21.3928 9.68886 21.7221 10.2513 21.9045C10.5637 22.0058 10.6314 22.0058 16.3855 21.9957L21.9133 21.9813C22.1067 21.9808 22.2974 21.9367 22.4703 21.8526L22.6031 21.788C23.3009 21.4536 23.7904 20.8558 23.9414 20.1415C24.0195 19.7716 24.0195 9.33019 23.9414 8.90463C23.8164 8.22576 23.2436 7.50129 22.624 7.23785C22.1136 7.02 21.9522 7.00987 18.6091 7.00987H16.6516C15.965 7.00987 15.4065 6.47245 15.4001 5.80543L15.3961 5.39881C15.3805 3.85868 15.3753 3.78269 15.2607 3.50405C14.9431 2.73399 14.2297 2.1767 13.4121 2.04498C13.0112 1.98419 2.25796 1.98419 1.92468 2.05005ZM13.2351 3.92961C13.4798 4.05627 13.4746 4.0208 13.4798 6.51338V7.66848C13.4798 8.33999 14.0402 8.88437 14.7314 8.88437H21.5562C21.6931 8.88437 21.8243 8.93722 21.921 9.03129C22.0177 9.12536 22.072 9.25288 22.072 9.38583V19.6731C22.072 19.7948 22.0235 19.9119 21.9366 19.9996C21.8464 20.0841 21.726 20.1314 21.6007 20.1314H16.3074C12.4748 20.1314 10.7876 20.1162 10.7095 20.0756C10.4804 19.954 10.4648 19.8071 10.4648 17.6945C10.4648 16.5951 10.4596 15.5768 10.4596 15.4299C10.4596 15.2844 10.3381 15.1665 10.1884 15.1665H6.3926C3.50252 15.1665 2.29441 15.1513 2.2163 15.1107C2.15381 15.0803 2.06008 15.0043 2.01321 14.9436C1.92989 14.8422 1.91948 14.4825 1.91948 9.50244V4.37029C1.91948 4.23733 1.97379 4.10982 2.07049 4.01574C2.16718 3.92167 2.29839 3.86882 2.43522 3.86882H7.67361C11.7145 3.86882 13.1518 3.88402 13.2351 3.92961Z" data-v-787ce242></path><path d="M7.02269 6.09795C7.02269 6.54563 6.64913 6.90854 6.18832 6.90854H4.76402C4.31431 6.90854 3.95218 7.26713 3.96076 7.70394L3.96097 7.71466C3.96955 8.15409 4.33703 8.50683 4.78942 8.50987L6.23638 8.5196C8.00168 8.52973 8.48596 8.54493 8.46513 8.59559C8.45472 8.62599 8.40264 8.77798 8.35578 8.91983C8.26725 9.17314 7.81942 9.90267 7.71006 9.96853C7.64758 10.004 7.46532 9.77602 7.23099 9.37579C7.14246 9.21874 7.04873 9.08701 7.03311 9.08701C6.94979 9.08701 5.56463 9.71522 5.56463 9.75575C5.56463 9.84188 5.83021 10.308 6.1114 10.7082C6.26762 10.9311 6.39781 11.1287 6.39781 11.1388C6.39781 11.1844 5.49173 11.6049 4.98141 11.7974C4.70021 11.9038 4.47108 12.0102 4.47108 12.0305C4.47108 12.1571 5.02306 13.4946 5.07514 13.4946C5.27302 13.4946 6.76232 12.8411 7.35076 12.4966C7.53446 12.3887 7.76628 12.3959 7.9425 12.515L8.02771 12.5726C8.91817 13.1704 10.0638 13.5757 10.9178 13.5959C10.9647 13.5959 10.9803 13.3984 10.9803 12.7853C10.9803 12.1774 10.9647 11.9697 10.9178 11.9545C10.8813 11.9444 10.6887 11.914 10.4856 11.8836C10.2304 11.843 9.97006 11.7518 9.60034 11.5745C8.81403 11.1895 8.84527 11.225 9.11605 10.8754C9.65241 10.1813 9.99089 9.47711 10.1679 8.66652C10.1862 8.5866 10.2591 8.52973 10.3434 8.52973H10.9803C11.4117 8.52973 11.7614 8.18999 11.7614 7.77089V7.71914C11.7614 7.27363 11.3913 6.90608 10.9327 6.90302L10.2408 6.89841L9.52631 6.89129C9.07464 6.88679 8.70867 6.53394 8.70062 6.09519L8.69946 6.03209L8.69891 5.98693C8.69344 5.54307 8.32151 5.18603 7.86459 5.18603H7.85587C7.39572 5.18603 7.02269 5.54843 7.02269 5.99546V6.09795Z" data-v-787ce242></path><path d="M14.8025 14.0367C14.2609 15.592 13.735 17.122 13.6308 17.4311C13.5358 17.7134 13.7524 18.0035 14.0581 18.0035H14.6221C14.9866 18.0035 15.309 17.7736 15.4182 17.4357L15.5095 17.1529C15.6187 16.815 15.941 16.585 16.3056 16.585H17.5745C17.8893 16.585 18.1673 16.7843 18.2602 17.0764C18.3487 17.35 18.4476 17.6692 18.4841 17.7857C18.521 17.9145 18.6418 18.0035 18.7794 18.0035H19.4527C19.8149 18.0035 20.1296 17.9954 20.2702 17.9811C20.3248 17.9755 20.3411 17.9372 20.3244 17.8864C20.2154 17.5551 19.747 16.1914 19.1871 14.5839L18.2146 11.7835C18.1015 11.4579 17.7893 11.2369 17.4356 11.2321L16.9063 11.225L16.3908 11.2203C16.0304 11.217 15.7085 11.439 15.5928 11.7707L14.8025 14.0367ZM17.3124 14.1431C17.4745 14.6485 17.6101 15.0894 17.6503 15.2571C17.6616 15.3045 17.6383 15.338 17.5885 15.3427C17.2918 15.3709 16.1893 15.374 16.1304 15.3387C16.1095 15.3286 16.146 15.1513 16.2137 14.9486C16.2814 14.751 16.4584 14.209 16.6042 13.7581C16.7153 13.4106 16.8234 13.1191 16.8755 13.0089C16.892 12.9741 16.916 12.9791 16.93 13.0149C16.9894 13.1658 17.1437 13.6212 17.3124 14.1431Z" data-v-787ce242></path></svg></span><span class="action-title" data-v-787ce242> dual subs </span>',
                                            2,
                                        ),
                                    ]),
                                10,
                                _hoisted_12,
                            ),
                            createBaseVNode(
                                "button",
                                {
                                    class: normalizeClass([
                                        "action action-time",
                                        {
                                            "action-active":
                                                unref(panelStore).isShowTime,
                                        },
                                    ]),
                                    onClick:
                                        _cache[4] ||
                                        (_cache[4] = (...args) =>
                                            unref(panelStore).toggleShowTime &&
                                            unref(panelStore).toggleShowTime(
                                                ...args,
                                            )),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                _cache[15] ||
                                    (_cache[15] = [
                                        createStaticVNode(
                                            '<span class="action-icon" data-v-787ce242><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-787ce242><path d="M11.2723 3.02455C7.42758 3.27849 4.16456 6.06278 3.2102 9.88098C2.92844 11.0237 2.92844 12.8739 3.21929 14.0801C3.9646 17.1455 6.27325 19.6487 9.22724 20.5919C10.218 20.9093 10.8633 21 11.9994 21C12.5993 21 13.3355 20.9547 13.6355 20.8912C17.1894 20.1656 19.8252 17.6897 20.7887 14.1889C21.0704 13.1822 21.0704 10.8061 20.7887 9.80842C20.0434 7.07855 18.2346 4.93818 15.7169 3.76824C14.4081 3.16966 12.8993 2.91572 11.2723 3.02455ZM13.6718 5.10143C16.1714 5.70001 18.1619 7.62271 18.88 10.144C19.1708 11.1598 19.1708 12.8285 18.88 13.8352C18.0528 16.7374 15.6078 18.7961 12.6993 19.0592C9.69988 19.3222 6.98221 17.8348 5.65519 15.1956C3.87371 11.6676 5.31889 7.36877 8.86367 5.62745C10.2816 4.92911 12.1085 4.72959 13.6718 5.10143Z" data-v-787ce242></path><path d="M11.6268 6.31676C11.0723 6.56163 11.0451 6.67953 11.0451 8.63851V10.4252L10.8088 10.6428C10.4452 10.9875 10.227 11.4772 10.227 11.9942C10.227 13.6085 12.0994 14.3885 13.2719 13.2639L13.5446 13.0009L15.0625 12.9737C16.5349 12.9465 16.5804 12.9374 16.8167 12.7197C17.1894 12.3751 17.2621 12.1121 17.0985 11.6767C16.8803 11.1144 16.7713 11.0782 15.1079 11.0782H13.6355L13.2992 10.7607L12.9538 10.4524V8.65665C12.9538 6.66139 12.9265 6.55256 12.3539 6.31676C11.9903 6.16258 11.9813 6.16258 11.6268 6.31676Z" data-v-787ce242></path></svg></span><span class="action-title" data-v-787ce242> show time </span>',
                                            2,
                                        ),
                                    ]),
                                10,
                                _hoisted_13,
                            ),
                            createBaseVNode(
                                "button",
                                {
                                    class: "action",
                                    onClick:
                                        _cache[5] ||
                                        (_cache[5] = (...args) =>
                                            unref(panelStore)
                                                .decreaseBlockSize &&
                                            unref(panelStore).decreaseBlockSize(
                                                ...args,
                                            )),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                _cache[16] ||
                                    (_cache[16] = [
                                        createStaticVNode(
                                            '<span class="action-icon" data-v-787ce242><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-787ce242><path fill-rule="evenodd" clip-rule="evenodd" d="M7.23891 3C4.89783 3 3 4.89782 3 7.23891V16.7611C3 19.1022 4.89782 21 7.23891 21H16.7611C19.1022 21 21 19.1022 21 16.7611V7.23891C21 4.89783 19.1022 3 16.7611 3H7.23891ZM7.42321 5.08874C6.13392 5.08874 5.08874 6.13392 5.08874 7.42321V16.5768C5.08874 17.8661 6.13392 18.9113 7.42321 18.9113H16.5768C17.8661 18.9113 18.9113 17.8661 18.9113 16.5768V7.42321C18.9113 6.13392 17.8661 5.08874 16.5768 5.08874H7.42321Z" data-v-787ce242></path><path d="M15.2867 11.0478H8.65188C8.24473 11.0478 7.91468 11.3778 7.91468 11.785V12.215C7.91468 12.6222 8.24473 12.9522 8.65188 12.9522H15.2867C15.6938 12.9522 16.0239 12.6222 16.0239 12.215V11.785C16.0239 11.3778 15.6938 11.0478 15.2867 11.0478Z" data-v-787ce242></path></svg></span><span class="action-title" data-v-787ce242> decrease </span>',
                                            2,
                                        ),
                                    ]),
                                8,
                                _hoisted_14,
                            ),
                            createBaseVNode(
                                "button",
                                {
                                    class: "action",
                                    onClick:
                                        _cache[6] ||
                                        (_cache[6] = (...args) =>
                                            unref(panelStore)
                                                .increaseBlockSize &&
                                            unref(panelStore).increaseBlockSize(
                                                ...args,
                                            )),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                _cache[17] ||
                                    (_cache[17] = [
                                        createStaticVNode(
                                            '<span class="action-icon" data-v-787ce242><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-787ce242><path fill-rule="evenodd" clip-rule="evenodd" d="M7.23891 3C4.89783 3 3 4.89782 3 7.23891V16.7611C3 19.1022 4.89782 21 7.23891 21H16.7611C19.1022 21 21 19.1022 21 16.7611V7.23891C21 4.89783 19.1022 3 16.7611 3H7.23891ZM7.42321 5.08874C6.13392 5.08874 5.08874 6.13392 5.08874 7.42321V16.5768C5.08874 17.8661 6.13392 18.9113 7.42321 18.9113H16.5768C17.8661 18.9113 18.9113 17.8661 18.9113 16.5768V7.42321C18.9113 6.13392 17.8661 5.08874 16.5768 5.08874H7.42321Z" data-v-787ce242></path><path d="M15.2867 11.0478H8.65188C8.24473 11.0478 7.91468 11.3778 7.91468 11.785V12.215C7.91468 12.6222 8.24473 12.9522 8.65188 12.9522H15.2867C15.6938 12.9522 16.0239 12.6222 16.0239 12.215V11.785C16.0239 11.3778 15.6938 11.0478 15.2867 11.0478Z" data-v-787ce242></path><path d="M11.0171 8.68259V15.3174C11.0171 15.7246 11.3471 16.0546 11.7543 16.0546H12.1843C12.5914 16.0546 12.9215 15.7246 12.9215 15.3174V8.68259C12.9215 8.27545 12.5914 7.94539 12.1843 7.94539H11.7543C11.3471 7.94539 11.0171 8.27545 11.0171 8.68259Z" data-v-787ce242></path></svg></span><span class="action-title" data-v-787ce242> increase </span>',
                                            2,
                                        ),
                                    ]),
                                8,
                                _hoisted_15,
                            ),
                        ]),
                    ]),
                    createBaseVNode("div", _hoisted_16, [
                        createBaseVNode("div", _hoisted_17, [
                            createBaseVNode(
                                "button",
                                {
                                    class: "action action-active",
                                    onClick:
                                        _cache[7] ||
                                        (_cache[7] = (...args) =>
                                            unref(panelStore).changeFormat &&
                                            unref(panelStore).changeFormat(
                                                ...args,
                                            )),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                [
                                    createBaseVNode(
                                        "span",
                                        _hoisted_19,
                                        toDisplayString(
                                            unref(panelStore).format,
                                        ),
                                        1,
                                    ),
                                    _cache[18] ||
                                        (_cache[18] = createBaseVNode(
                                            "span",
                                            { class: "action-title" },
                                            " format ",
                                            -1,
                                        )),
                                ],
                                8,
                                _hoisted_18,
                            ),
                            createBaseVNode(
                                "button",
                                {
                                    class: "action",
                                    onClick:
                                        _cache[8] ||
                                        (_cache[8] = (...args) =>
                                            unref(subtitlesStore)
                                                .downloadSubtitles &&
                                            unref(
                                                subtitlesStore,
                                            ).downloadSubtitles(...args)),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                _cache[19] ||
                                    (_cache[19] = [
                                        createStaticVNode(
                                            '<span class="action-icon" data-v-787ce242><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-787ce242><path fill-rule="evenodd" clip-rule="evenodd" d="M9.75768 8.9079H7.23891C4.89783 8.9079 3 10.8579 3 13.2634V16.9876C3 19.3931 4.89782 21.3431 7.23891 21.3431H16.7611C19.1022 21.3431 21 19.3931 21 16.9876V13.2634C21 10.8579 19.1022 8.9079 16.7611 8.9079H14.1809V10.8016H16.8225C18.1118 10.8016 19.157 11.8755 19.157 13.2003V17.0508C19.157 18.3755 18.1118 19.4494 16.8225 19.4494H7.17747C5.88818 19.4494 4.843 18.3755 4.843 17.0508V13.2003C4.843 11.8755 5.88818 10.8016 7.17747 10.8016H9.75768V8.9079Z" data-v-787ce242></path><path d="M11.0478 4.23681V13.074C11.0478 13.4924 11.3778 13.8315 11.785 13.8315H12.1536C12.5607 13.8315 12.8908 13.4924 12.8908 13.074V4.23681C12.8908 3.81846 12.5607 3.47933 12.1536 3.47933H11.785C11.3778 3.47933 11.0478 3.81846 11.0478 4.23681Z" data-v-787ce242></path><path d="M11.4304 2.87874L8.48609 5.90399C8.19819 6.1998 8.19819 6.67941 8.48609 6.97522L8.74673 7.24303C9.03462 7.53884 9.50139 7.53884 9.78929 7.24303L12.7336 4.21778C13.0215 3.92196 13.0215 3.44236 12.7336 3.14654L12.4729 2.87874C12.185 2.58292 11.7183 2.58292 11.4304 2.87874Z" data-v-787ce242></path><path d="M15.358 5.83008L12.4856 2.87873C12.1978 2.58292 11.731 2.58292 11.4431 2.87873L11.1824 3.14654C10.8946 3.44236 10.8946 3.92196 11.1824 4.21777L14.0548 7.16912C14.3427 7.46493 14.8095 7.46493 15.0974 7.16912L15.358 6.90131C15.6459 6.6055 15.6459 6.12589 15.358 5.83008Z" data-v-787ce242></path></svg></span><span class="action-title" data-v-787ce242> download </span>',
                                            2,
                                        ),
                                    ]),
                                8,
                                _hoisted_20,
                            ),
                            createBaseVNode(
                                "button",
                                {
                                    class: "action",
                                    onClick:
                                        _cache[9] ||
                                        (_cache[9] = (...args) =>
                                            unref(subtitlesStore)
                                                .copySubtitles &&
                                            unref(subtitlesStore).copySubtitles(
                                                ...args,
                                            )),
                                    disabled:
                                        unref(subtitlesStore).subtitles
                                            .length === 0,
                                },
                                _cache[20] ||
                                    (_cache[20] = [
                                        createBaseVNode(
                                            "span",
                                            { class: "action-icon" },
                                            [
                                                createBaseVNode(
                                                    "svg",
                                                    {
                                                        width: "24",
                                                        height: "24",
                                                        viewBox: "0 0 24 24",
                                                        fill: "none",
                                                        xmlns: "http://www.w3.org/2000/svg",
                                                    },
                                                    [
                                                        createBaseVNode(
                                                            "path",
                                                            {
                                                                d: "M6.55491 3.06234C5.47977 3.21838 4.2659 4.05057 3.63295 5.04746C3.49422 5.26418 3.2948 5.70628 3.19075 6.04436C3.00867 6.62516 3 6.78986 3 9.77188C3 12.1124 3.02601 12.9879 3.11272 13.274C3.5896 14.8517 4.89017 16.1087 6.33815 16.3861C6.76301 16.4727 7.6474 16.5681 8.11561 16.5768C8.27168 16.5768 8.30636 16.6721 8.40173 17.2443C8.67919 19.0387 10.0838 20.547 11.7832 20.9024C12.4942 21.0498 16.9075 21.0238 17.6358 20.8678C18.7803 20.625 19.9855 19.7062 20.5058 18.6746C20.948 17.8077 21 17.3309 21 14.2536C21 12.6325 20.9566 11.2109 20.9046 10.9421C20.6705 9.7112 19.7428 8.49759 18.6069 7.95146C17.896 7.59605 17.3237 7.47469 16.3786 7.47469C15.9104 7.47469 15.685 7.44001 15.6676 7.36199C15.6503 7.30998 15.5896 6.96324 15.5376 6.60782C15.3555 5.43755 14.7919 4.52734 13.7688 3.78184C12.8324 3.08835 12.4769 3.02767 9.5896 3.00166C8.21098 2.99299 6.84104 3.019 6.55491 3.06234ZM11.2023 4.95211C12.1734 5.04746 12.5723 5.22084 13.0491 5.74096C13.6734 6.43445 13.6647 6.3911 13.6647 9.72854V12.7192L13.4133 13.222C13.2572 13.5601 13.0318 13.8375 12.7457 14.0629C12.1387 14.5396 11.6445 14.609 8.93931 14.5656C7.04913 14.5396 6.6763 14.5136 6.3815 14.3836C5.94798 14.1929 5.42775 13.6901 5.18497 13.2393C5.00289 12.9099 4.99422 12.7799 4.96821 9.96259C4.93353 6.5298 4.96821 6.33042 5.60983 5.68894C6.09538 5.2035 6.46821 5.03879 7.24855 4.96078C8.21098 4.87409 10.2919 4.86542 11.2023 4.95211ZM17.7572 9.69386C18.2341 9.92791 18.6416 10.3613 18.8931 10.9161C19.0231 11.2109 19.0405 11.5836 19.0405 14.2362C19.0405 17.1056 19.0318 17.2443 18.8584 17.6257C18.6329 18.1025 18.1908 18.5706 17.7399 18.7786C17.2197 19.03 16.3266 19.108 14.2023 19.0733C12.3035 19.0473 12.1647 19.0387 11.7399 18.8393C10.9682 18.4925 10.3353 17.617 10.3266 16.8975C10.3266 16.6201 10.3526 16.5768 10.526 16.5681C10.9335 16.5594 11.7746 16.4727 12.2341 16.3947C13.7514 16.1347 15.1908 14.713 15.5202 13.1353C15.5723 12.8926 15.6156 11.9737 15.6243 11.0982C15.6329 10.2226 15.659 9.47714 15.685 9.4338C15.7023 9.39046 16.0838 9.38179 16.5173 9.41646C17.1069 9.45114 17.4191 9.52916 17.7572 9.69386Z",
                                                            },
                                                        ),
                                                    ],
                                                ),
                                            ],
                                            -1,
                                        ),
                                        createBaseVNode(
                                            "span",
                                            { class: "action-title" },
                                            " copy ",
                                            -1,
                                        ),
                                    ]),
                                8,
                                _hoisted_21,
                            ),
                        ]),
                        createBaseVNode("div", _hoisted_22, [
                            createBaseVNode(
                                "button",
                                {
                                    class: "action",
                                    onClick:
                                        _cache[10] ||
                                        (_cache[10] = (...args) =>
                                            unref(panelStore).changeTheme &&
                                            unref(panelStore).changeTheme(
                                                ...args,
                                            )),
                                },
                                [
                                    createBaseVNode("span", _hoisted_23, [
                                        unref(panelStore).isLightTheme
                                            ? (openBlock(),
                                              createElementBlock(
                                                  "svg",
                                                  _hoisted_24,
                                                  _cache[21] ||
                                                      (_cache[21] = [
                                                          createBaseVNode(
                                                              "path",
                                                              {
                                                                  d: "M8.93162 3.27441C6.09027 4.37605 3.91676 6.82145 3.20035 9.73899C2.93322 10.8406 2.93322 12.9713 3.20035 14.0003C4.13532 17.5594 6.91596 20.1622 10.5101 20.8401C14.6993 21.6391 18.8399 19.3874 20.6127 15.3319C21.062 14.2908 21.1227 13.6492 20.7827 13.0318C20.5399 12.5718 19.7142 12.0633 19.2163 12.0633C19.0463 12.0633 18.4028 12.3176 17.7714 12.6323C16.7878 13.1165 16.5207 13.2013 15.695 13.2497C14.505 13.3102 13.6672 13.0923 12.7565 12.4628C11.5058 11.5912 10.6923 10.0658 10.6923 8.62524C10.6923 7.82625 11.0687 6.59144 11.4937 5.96193C12.1737 4.96924 11.9187 3.72233 10.9351 3.22599C10.328 2.91123 9.8423 2.92334 8.93162 3.27441ZM8.56734 6.62776C8.26378 7.45096 8.20307 8.9521 8.43377 10.0053C9.02876 12.826 11.3723 15.114 14.1408 15.5862C15.0636 15.7435 16.7028 15.6709 17.3464 15.4409C17.5649 15.3682 17.7835 15.3319 17.8199 15.3804C17.8564 15.4167 17.7107 15.683 17.4799 15.9735C15.4521 18.54 12.028 19.3148 9.08947 17.85C7.21952 16.9178 5.94456 15.223 5.50743 13.0923C5.00959 10.6469 6.09027 7.91099 8.13021 6.42196C8.43377 6.20405 8.70091 6.04667 8.71305 6.05878C8.73734 6.08299 8.66448 6.33722 8.56734 6.62776Z",
                                                              },
                                                              null,
                                                              -1,
                                                          ),
                                                      ]),
                                              ))
                                            : (openBlock(),
                                              createElementBlock(
                                                  "svg",
                                                  _hoisted_25,
                                                  _cache[22] ||
                                                      (_cache[22] = [
                                                          createStaticVNode(
                                                              '<path d="M11.442 2.28502C11.2292 2.4964 11.2203 2.53163 11.2203 3.90558V5.31476L11.4597 5.49971C11.7878 5.75513 12.3375 5.71109 12.5592 5.41164C12.7011 5.22668 12.7277 5.01531 12.7365 4.0465C12.7365 3.41237 12.7365 2.81346 12.7365 2.69897C12.7188 2.1353 11.8676 1.85346 11.442 2.28502Z" data-v-787ce242></path><path d="M5.09336 4.83921C4.8185 4.98894 4.61456 5.41169 4.6855 5.69353C4.72096 5.80802 5.17316 6.32766 5.70516 6.84729C6.70709 7.82491 6.90216 7.92179 7.32776 7.70161C7.57602 7.56069 7.77995 7.11151 7.71789 6.82968C7.65582 6.53903 6.05096 4.93609 5.69629 4.80398C5.38596 4.68949 5.37709 4.68949 5.09336 4.83921Z" data-v-787ce242></path><path d="M17.2318 5.65836C16.354 6.53029 16.2742 6.63598 16.2742 6.93543C16.2742 7.39342 16.5845 7.70167 17.0456 7.70167C17.347 7.70167 17.4534 7.62241 18.3312 6.75048C19.209 5.87855 19.2888 5.77286 19.2888 5.47341C19.2888 5.23561 19.2268 5.07708 19.0672 4.92735C18.9164 4.76882 18.7568 4.70717 18.5174 4.70717C18.216 4.70717 18.1096 4.78643 17.2318 5.65836Z" data-v-787ce242></path><path d="M10.7414 7.0409C9.571 7.34035 8.3474 8.20347 7.67354 9.22513C6.93761 10.3437 6.67161 11.9554 7.01741 13.2853C7.64694 15.7426 10.0941 17.3808 12.6477 17.0373C15.3166 16.685 17.4002 14.1044 17.1165 11.4974C16.9835 10.2908 16.4781 9.20751 15.6713 8.37962C15.2368 7.93925 14.5541 7.46365 14.2703 7.39319C14.1728 7.36677 13.8625 7.26989 13.5699 7.1642C12.8783 6.9176 11.4862 6.85594 10.7414 7.0409ZM13.0645 8.96971C13.5344 9.13705 14.3413 9.6743 14.5629 9.97375C14.9885 10.5462 15.1481 10.9954 15.1925 11.7176C15.2457 12.6336 15.0683 13.2413 14.5718 13.893C13.8447 14.8354 12.9226 15.2494 11.7079 15.1701C10.5375 15.0908 9.58873 14.4391 9.05673 13.3382C8.80847 12.8273 8.78187 12.6776 8.78187 11.973C8.78187 11.3389 8.8262 11.0923 8.9858 10.7488C9.71287 9.17228 11.4596 8.40604 13.0645 8.96971Z" data-v-787ce242></path><path d="M18.7657 11.2598C18.2781 11.5328 18.1894 12.0788 18.5707 12.4664L18.8278 12.7218H20.1578C21.3459 12.7218 21.5055 12.7042 21.7094 12.5456C22.1439 12.2021 22.0818 11.524 21.5853 11.2686C21.2484 11.0924 19.0761 11.0924 18.7657 11.2598Z" data-v-787ce242></path><path d="M2.22057 11.4449C1.92797 11.7267 1.92797 12.1847 2.2117 12.5194L2.4245 12.766H5.20863L5.42143 12.5194C5.71403 12.1847 5.70516 11.762 5.4037 11.4537C5.1643 11.2247 5.15543 11.2247 3.79883 11.2247C2.46884 11.2247 2.4245 11.2335 2.22057 11.4449Z" data-v-787ce242></path><path d="M16.6732 16.2624C16.3895 16.4209 16.2831 16.6147 16.2742 16.9582C16.2742 17.24 16.3629 17.3633 17.1963 18.1824C17.7106 18.6844 18.216 19.1336 18.3135 19.1688C18.7834 19.3362 19.2888 18.9222 19.2888 18.3762C19.2888 18.1032 19.1913 17.971 18.4731 17.24C17.4091 16.1743 17.1165 16.0158 16.6732 16.2624Z" data-v-787ce242></path><path d="M5.74949 17.055C4.57909 18.2 4.43722 18.5083 4.91602 18.9839C5.27069 19.3362 5.61649 19.3273 6.04209 18.9486C6.63615 18.4378 7.62922 17.3809 7.70015 17.196C7.85975 16.7556 7.46075 16.2448 6.93762 16.2448C6.60955 16.2448 6.52089 16.3064 5.74949 17.055Z" data-v-787ce242></path><path d="M11.4952 18.4995L11.2203 18.7285V20.076C11.2203 21.3707 11.2292 21.4411 11.4331 21.6701C11.7169 21.996 12.1957 22.0136 12.5149 21.6966C12.7277 21.4852 12.7365 21.4411 12.7365 20.1641C12.7365 19.4331 12.7099 18.7725 12.6833 18.6844C12.6035 18.4995 12.1957 18.2705 11.9385 18.2705C11.841 18.2705 11.6459 18.3762 11.4952 18.4995Z" data-v-787ce242></path>',
                                                              9,
                                                          ),
                                                      ]),
                                              )),
                                    ]),
                                    _cache[23] ||
                                        (_cache[23] = createBaseVNode(
                                            "span",
                                            { class: "action-title" },
                                            " theme ",
                                            -1,
                                        )),
                                ],
                            ),
                        ]),
                    ]),
                ])
            );
        };
    },
};
const YsdPanel = /* @__PURE__ */ _export_sfc(_sfc_main$3, [
    ["__scopeId", "data-v-787ce242"],
]);
const _hoisted_1$2 = {
    key: 0,
    class: "error-message",
};
const _hoisted_2$2 = { class: "subtitle-header" };
const _hoisted_3$2 = ["onClick"];
const _hoisted_4$2 = {
    key: 0,
    class: "text-original",
};
const _hoisted_5$1 = {
    key: 1,
    class: "text-translate",
};
const _sfc_main$2 = {
    __name: "YsdSubtitles",
    setup(__props) {
        const panelStore = usePanelStore();
        const videoInfoStore = useVideoInfoStore();
        const subtitlesStore = useSubtitlesStore();
        const toggleShowOriginal = () => {
            if (subtitlesStore.isShowTranslate === false) {
                return;
            }
            subtitlesStore.isShowOriginal = !subtitlesStore.isShowOriginal;
        };
        const toggleShowTranslate = () => {
            if (subtitlesStore.isShowOriginal === false) {
                return;
            }
            subtitlesStore.isShowTranslate = !subtitlesStore.isShowTranslate;
        };
        const toggleTranslateFirst = () => {
            subtitlesStore.isTranslateFirst = !subtitlesStore.isTranslateFirst;
        };
        const subtitlesScrollContainer = ref(null);
        watch(
            () => videoInfoStore.currentTime,
            () => {
                if (
                    panelStore.isAutoScrollEnabled &&
                    subtitlesScrollContainer.value &&
                    subtitlesStore.activeSubtitle
                ) {
                    nextTick(() => {
                        subtitlesStore.scrollToActiveSubtitle();
                    });
                }
            },
        );
        return (_ctx, _cache) => {
            return (
                openBlock(),
                createElementBlock(
                    "div",
                    {
                        class: "subtitles",
                        ref_key: "subtitlesScrollContainer",
                        ref: subtitlesScrollContainer,
                    },
                    [
                        unref(subtitlesStore).subtitles.length === 0
                            ? (openBlock(),
                              createElementBlock(
                                  "div",
                                  _hoisted_1$2,
                                  " There are no subtitles in the video ",
                              ))
                            : createCommentVNode("", true),
                        (openBlock(true),
                        createElementBlock(
                            Fragment,
                            null,
                            renderList(
                                unref(subtitlesStore).mergedSubtitles,
                                (sub, index) => {
                                    return (
                                        openBlock(),
                                        createElementBlock(
                                            "div",
                                            {
                                                class: "subtitle",
                                                key: index,
                                            },
                                            [
                                                createBaseVNode(
                                                    "div",
                                                    _hoisted_2$2,
                                                    [
                                                        unref(panelStore)
                                                            .isShowTime
                                                            ? (openBlock(),
                                                              createElementBlock(
                                                                  "div",
                                                                  {
                                                                      key: 0,
                                                                      class: "time",
                                                                      onClick: (
                                                                          $event,
                                                                      ) =>
                                                                          unref(
                                                                              videoInfoStore,
                                                                          ).jumpToTime(
                                                                              sub.start,
                                                                          ),
                                                                  },
                                                                  toDisplayString(
                                                                      sub.startInFormat,
                                                                  ),
                                                                  9,
                                                                  _hoisted_3$2,
                                                              ))
                                                            : createCommentVNode(
                                                                  "",
                                                                  true,
                                                              ),
                                                        unref(panelStore)
                                                            .isDualSubtitles &&
                                                        unref(subtitlesStore)
                                                            .translations
                                                            .length > 0
                                                            ? (openBlock(),
                                                              createElementBlock(
                                                                  "div",
                                                                  {
                                                                      key: 1,
                                                                      class: normalizeClass(
                                                                          [
                                                                              "subtitle-buttons",
                                                                              {
                                                                                  "subtitle-buttons-reverse":
                                                                                      unref(
                                                                                          subtitlesStore,
                                                                                      )
                                                                                          .isTranslateFirst,
                                                                              },
                                                                          ],
                                                                      ),
                                                                  },
                                                                  [
                                                                      createBaseVNode(
                                                                          "button",
                                                                          {
                                                                              class: normalizeClass(
                                                                                  {
                                                                                      active: unref(
                                                                                          subtitlesStore,
                                                                                      )
                                                                                          .isShowOriginal,
                                                                                  },
                                                                              ),
                                                                              onClick:
                                                                                  toggleShowOriginal,
                                                                          },
                                                                          "original",
                                                                          2,
                                                                      ),
                                                                      createBaseVNode(
                                                                          "button",
                                                                          {
                                                                              onClick:
                                                                                  toggleTranslateFirst,
                                                                          },
                                                                          _cache[0] ||
                                                                              (_cache[0] =
                                                                                  [
                                                                                      createBaseVNode(
                                                                                          "svg",
                                                                                          {
                                                                                              width: "10",
                                                                                              height: "14",
                                                                                              viewBox:
                                                                                                  "0 0 10 14",
                                                                                              fill: "none",
                                                                                              xmlns: "http://www.w3.org/2000/svg",
                                                                                          },
                                                                                          [
                                                                                              createBaseVNode(
                                                                                                  "path",
                                                                                                  {
                                                                                                      d: "M6.16082 0.548516C6.00834 0.615063 5.71944 0.88125 5.62849 1.0425C5.56696 1.14744 5.55091 1.21142 5.55091 1.31892C5.55091 1.59023 5.56963 1.61582 6.2384 2.25825L6.85634 2.85461L0.492356 2.86741L0.385353 2.92116C0.123197 3.05681 0.0188691 3.23086 0.00281868 3.56871C-0.0185819 3.99614 0.0803956 4.22138 0.350577 4.35703L0.492356 4.4287L6.82959 4.44149L6.27853 4.97387C5.89064 5.34499 5.70874 5.53695 5.67129 5.61886C5.59638 5.77755 5.59906 5.97975 5.67931 6.14355C5.75154 6.29456 6.09662 6.62474 6.24108 6.68104C6.3775 6.73735 6.64501 6.72967 6.77609 6.66825C6.84832 6.63497 7.35658 6.16915 8.26878 5.2938C9.63306 3.98591 9.65179 3.96543 9.69459 3.81954C9.74274 3.66085 9.73204 3.52776 9.66249 3.36651C9.63841 3.30764 9.14887 2.82134 8.24203 1.94856C7.1506 0.901726 6.82959 0.609944 6.72526 0.561314C6.55673 0.484529 6.32133 0.47941 6.16082 0.548516Z",
                                                                                                  },
                                                                                              ),
                                                                                              createBaseVNode(
                                                                                                  "path",
                                                                                                  {
                                                                                                      d: "M3.21825 7.32092C3.11927 7.36699 2.75546 7.69972 1.72021 8.69536C0.24625 10.1082 0.281025 10.0698 0.281025 10.3411C0.281025 10.615 0.240899 10.5689 1.77371 12.038C3.05775 13.2692 3.20755 13.4048 3.33595 13.4509C3.64091 13.5609 3.85759 13.4918 4.16522 13.1847C4.3391 13.0107 4.38993 12.9441 4.42203 12.834C4.47553 12.6651 4.47553 12.6242 4.42203 12.4501C4.3819 12.3222 4.33643 12.271 3.76129 11.7181L3.14335 11.1218L9.53408 11.109L9.64376 11.0526C9.76146 10.9912 9.87114 10.8837 9.94069 10.7532C9.99687 10.6482 10.0209 10.2157 9.97814 10.034C9.94069 9.87272 9.78822 9.691 9.61701 9.60398L9.50733 9.54767L3.1701 9.53487L3.74791 8.9769C4.36585 8.3831 4.38458 8.35751 4.38458 8.10412C4.38458 7.90448 4.33375 7.79954 4.1251 7.5871C3.88434 7.34395 3.71849 7.25693 3.50181 7.25693C3.3948 7.25693 3.31188 7.27484 3.21825 7.32092Z",
                                                                                                  },
                                                                                              ),
                                                                                          ],
                                                                                          -1,
                                                                                      ),
                                                                                  ]),
                                                                      ),
                                                                      createBaseVNode(
                                                                          "button",
                                                                          {
                                                                              class: normalizeClass(
                                                                                  {
                                                                                      active: unref(
                                                                                          subtitlesStore,
                                                                                      )
                                                                                          .isShowTranslate,
                                                                                  },
                                                                              ),
                                                                              onClick:
                                                                                  toggleShowTranslate,
                                                                          },
                                                                          "translate",
                                                                          2,
                                                                      ),
                                                                  ],
                                                                  2,
                                                              ))
                                                            : createCommentVNode(
                                                                  "",
                                                                  true,
                                                              ),
                                                    ],
                                                ),
                                                createBaseVNode(
                                                    "div",
                                                    {
                                                        class: normalizeClass([
                                                            "text",
                                                            {
                                                                "text-reverse":
                                                                    unref(
                                                                        subtitlesStore,
                                                                    )
                                                                        .isTranslateFirst,
                                                            },
                                                        ]),
                                                    },
                                                    [
                                                        unref(subtitlesStore)
                                                            .isShowOriginal ||
                                                        !unref(panelStore)
                                                            .isDualSubtitles
                                                            ? (openBlock(),
                                                              createElementBlock(
                                                                  "div",
                                                                  _hoisted_4$2,
                                                                  [
                                                                      (openBlock(
                                                                          true,
                                                                      ),
                                                                      createElementBlock(
                                                                          Fragment,
                                                                          null,
                                                                          renderList(
                                                                              sub.textSegments,
                                                                              (
                                                                                  segment,
                                                                                  TimesIndex,
                                                                              ) => {
                                                                                  return (
                                                                                      openBlock(),
                                                                                      createElementBlock(
                                                                                          "span",
                                                                                          {
                                                                                              key: _ctx.sIndex,
                                                                                              class: normalizeClass(
                                                                                                  {
                                                                                                      "highlighted-text":
                                                                                                          segment.start <=
                                                                                                              unref(
                                                                                                                  videoInfoStore,
                                                                                                              )
                                                                                                                  .currentTime &&
                                                                                                          unref(
                                                                                                              videoInfoStore,
                                                                                                          )
                                                                                                              .currentTime <=
                                                                                                              segment.end,
                                                                                                  },
                                                                                              ),
                                                                                          },
                                                                                          toDisplayString(
                                                                                              segment.text,
                                                                                          ) +
                                                                                              "  ",
                                                                                          3,
                                                                                      )
                                                                                  );
                                                                              },
                                                                          ),
                                                                          128,
                                                                      )),
                                                                  ],
                                                              ))
                                                            : createCommentVNode(
                                                                  "",
                                                                  true,
                                                              ),
                                                        unref(panelStore)
                                                            .isShowTranslate &&
                                                        unref(subtitlesStore)
                                                            .isShowTranslate
                                                            ? (openBlock(),
                                                              createElementBlock(
                                                                  "div",
                                                                  _hoisted_5$1,
                                                                  [
                                                                      (openBlock(
                                                                          true,
                                                                      ),
                                                                      createElementBlock(
                                                                          Fragment,
                                                                          null,
                                                                          renderList(
                                                                              sub.translateSegments,
                                                                              (
                                                                                  segment,
                                                                                  tIndex,
                                                                              ) => {
                                                                                  return (
                                                                                      openBlock(),
                                                                                      createElementBlock(
                                                                                          "span",
                                                                                          {
                                                                                              key: tIndex,
                                                                                              class: normalizeClass(
                                                                                                  {
                                                                                                      "highlighted-translate":
                                                                                                          segment.start <=
                                                                                                              unref(
                                                                                                                  videoInfoStore,
                                                                                                              )
                                                                                                                  .currentTime &&
                                                                                                          unref(
                                                                                                              videoInfoStore,
                                                                                                          )
                                                                                                              .currentTime <=
                                                                                                              segment.end,
                                                                                                  },
                                                                                              ),
                                                                                          },
                                                                                          toDisplayString(
                                                                                              segment.text,
                                                                                          ) +
                                                                                              "  ",
                                                                                          3,
                                                                                      )
                                                                                  );
                                                                              },
                                                                          ),
                                                                          128,
                                                                      )),
                                                                  ],
                                                              ))
                                                            : createCommentVNode(
                                                                  "",
                                                                  true,
                                                              ),
                                                    ],
                                                    2,
                                                ),
                                            ],
                                        )
                                    );
                                },
                            ),
                            128,
                        )),
                    ],
                    512,
                )
            );
        };
    },
};
const YsdSubtitles = /* @__PURE__ */ _export_sfc(_sfc_main$2, [
    ["__scopeId", "data-v-7516df5a"],
]);
const _hoisted_1$1 = { class: "ysd-rating" };
const _hoisted_2$1 = { class: "ysd-rating-form" };
const _hoisted_3$1 = { class: "ysd-rating-title" };
const _hoisted_4$1 = {
    key: 0,
    class: "ysd-rating-stars",
};
const _hoisted_5 = {
    key: 1,
    class: "thumbs",
};
const _hoisted_6 = {
    key: 2,
    class: "ysd-write-review",
};
const _sfc_main$1 = {
    __name: "YsdRating",
    setup(__props) {
        const userStore = useUserStore();
        const configStore = useConfigStore();
        const rate = async (stars) => {
            configStore.rating.show = false;
            try {
                const ratingUrl = new URL(
                    `https://antonkhoteev.com/khoteev-api/ysd/rating/${stars}`,
                );
                if (userStore.userId)
                    ratingUrl.searchParams.append("userId", userStore.userId);
                if (stars === 5) {
                    window.open(ratingUrl.toString(), "_blank");
                } else {
                    await fetch(ratingUrl, { method: "GET" });
                }
            } catch (e) {}
        };
        return (_ctx, _cache) => {
            return (
                openBlock(),
                createElementBlock("div", _hoisted_1$1, [
                    createBaseVNode("div", _hoisted_2$1, [
                        createBaseVNode(
                            "div",
                            _hoisted_3$1,
                            toDisplayString(unref(configStore).rating.title),
                            1,
                        ),
                        unref(configStore).rating.type === "stars"
                            ? (openBlock(),
                              createElementBlock("div", _hoisted_4$1, [
                                  createBaseVNode(
                                      "a",
                                      {
                                          onClick:
                                              _cache[0] ||
                                              (_cache[0] = ($event) => rate(5)),
                                      },
                                      _cache[9] ||
                                          (_cache[9] = [
                                              createBaseVNode(
                                                  "svg",
                                                  {
                                                      width: "18",
                                                      height: "17",
                                                      viewBox: "0 0 24 22",
                                                      fill: "none",
                                                      xmlns: "http://www.w3.org/2000/svg",
                                                  },
                                                  [
                                                      createBaseVNode("path", {
                                                          d: "M10.2455 1.20652C11.0042 -0.180108 12.9958 -0.180108 13.7545 1.20652L15.9155 5.15589C16.203 5.68124 16.7107 6.05014 17.2992 6.16118L21.723 6.99598C23.2763 7.28908 23.8917 9.1832 22.8074 10.3333L19.7191 13.6089C19.3083 14.0447 19.1143 14.6415 19.1906 15.2355L19.7637 19.7008C19.9649 21.2686 18.3537 22.4392 16.9248 21.7634L12.8551 19.8385C12.3138 19.5824 11.6862 19.5824 11.1449 19.8385L7.07519 21.7634C5.64633 22.4392 4.0351 21.2686 4.23631 19.7008L4.80942 15.2355C4.88566 14.6415 4.69172 14.0447 4.28091 13.6089L1.19262 10.3333C0.108311 9.1832 0.723747 7.28908 2.27697 6.99598L6.70083 6.16118C7.28929 6.05014 7.79703 5.68124 8.08449 5.15589L10.2455 1.20652Z",
                                                      }),
                                                  ],
                                                  -1,
                                              ),
                                          ]),
                                  ),
                                  createBaseVNode(
                                      "a",
                                      {
                                          onClick:
                                              _cache[1] ||
                                              (_cache[1] = ($event) => rate(4)),
                                      },
                                      _cache[10] ||
                                          (_cache[10] = [
                                              createBaseVNode(
                                                  "svg",
                                                  {
                                                      width: "18",
                                                      height: "17",
                                                      viewBox: "0 0 24 22",
                                                      fill: "none",
                                                      xmlns: "http://www.w3.org/2000/svg",
                                                  },
                                                  [
                                                      createBaseVNode("path", {
                                                          d: "M10.2455 1.20652C11.0042 -0.180108 12.9958 -0.180108 13.7545 1.20652L15.9155 5.15589C16.203 5.68124 16.7107 6.05014 17.2992 6.16118L21.723 6.99598C23.2763 7.28908 23.8917 9.1832 22.8074 10.3333L19.7191 13.6089C19.3083 14.0447 19.1143 14.6415 19.1906 15.2355L19.7637 19.7008C19.9649 21.2686 18.3537 22.4392 16.9248 21.7634L12.8551 19.8385C12.3138 19.5824 11.6862 19.5824 11.1449 19.8385L7.07519 21.7634C5.64633 22.4392 4.0351 21.2686 4.23631 19.7008L4.80942 15.2355C4.88566 14.6415 4.69172 14.0447 4.28091 13.6089L1.19262 10.3333C0.108311 9.1832 0.723747 7.28908 2.27697 6.99598L6.70083 6.16118C7.28929 6.05014 7.79703 5.68124 8.08449 5.15589L10.2455 1.20652Z",
                                                      }),
                                                  ],
                                                  -1,
                                              ),
                                          ]),
                                  ),
                                  createBaseVNode(
                                      "a",
                                      {
                                          onClick:
                                              _cache[2] ||
                                              (_cache[2] = ($event) => rate(3)),
                                      },
                                      _cache[11] ||
                                          (_cache[11] = [
                                              createBaseVNode(
                                                  "svg",
                                                  {
                                                      width: "18",
                                                      height: "17",
                                                      viewBox: "0 0 24 22",
                                                      fill: "none",
                                                      xmlns: "http://www.w3.org/2000/svg",
                                                  },
                                                  [
                                                      createBaseVNode("path", {
                                                          d: "M10.2455 1.20652C11.0042 -0.180108 12.9958 -0.180108 13.7545 1.20652L15.9155 5.15589C16.203 5.68124 16.7107 6.05014 17.2992 6.16118L21.723 6.99598C23.2763 7.28908 23.8917 9.1832 22.8074 10.3333L19.7191 13.6089C19.3083 14.0447 19.1143 14.6415 19.1906 15.2355L19.7637 19.7008C19.9649 21.2686 18.3537 22.4392 16.9248 21.7634L12.8551 19.8385C12.3138 19.5824 11.6862 19.5824 11.1449 19.8385L7.07519 21.7634C5.64633 22.4392 4.0351 21.2686 4.23631 19.7008L4.80942 15.2355C4.88566 14.6415 4.69172 14.0447 4.28091 13.6089L1.19262 10.3333C0.108311 9.1832 0.723747 7.28908 2.27697 6.99598L6.70083 6.16118C7.28929 6.05014 7.79703 5.68124 8.08449 5.15589L10.2455 1.20652Z",
                                                      }),
                                                  ],
                                                  -1,
                                              ),
                                          ]),
                                  ),
                                  createBaseVNode(
                                      "a",
                                      {
                                          onClick:
                                              _cache[3] ||
                                              (_cache[3] = ($event) => rate(2)),
                                      },
                                      _cache[12] ||
                                          (_cache[12] = [
                                              createBaseVNode(
                                                  "svg",
                                                  {
                                                      width: "18",
                                                      height: "17",
                                                      viewBox: "0 0 24 22",
                                                      fill: "none",
                                                      xmlns: "http://www.w3.org/2000/svg",
                                                  },
                                                  [
                                                      createBaseVNode("path", {
                                                          d: "M10.2455 1.20652C11.0042 -0.180108 12.9958 -0.180108 13.7545 1.20652L15.9155 5.15589C16.203 5.68124 16.7107 6.05014 17.2992 6.16118L21.723 6.99598C23.2763 7.28908 23.8917 9.1832 22.8074 10.3333L19.7191 13.6089C19.3083 14.0447 19.1143 14.6415 19.1906 15.2355L19.7637 19.7008C19.9649 21.2686 18.3537 22.4392 16.9248 21.7634L12.8551 19.8385C12.3138 19.5824 11.6862 19.5824 11.1449 19.8385L7.07519 21.7634C5.64633 22.4392 4.0351 21.2686 4.23631 19.7008L4.80942 15.2355C4.88566 14.6415 4.69172 14.0447 4.28091 13.6089L1.19262 10.3333C0.108311 9.1832 0.723747 7.28908 2.27697 6.99598L6.70083 6.16118C7.28929 6.05014 7.79703 5.68124 8.08449 5.15589L10.2455 1.20652Z",
                                                      }),
                                                  ],
                                                  -1,
                                              ),
                                          ]),
                                  ),
                                  createBaseVNode(
                                      "a",
                                      {
                                          onClick:
                                              _cache[4] ||
                                              (_cache[4] = ($event) => rate(1)),
                                      },
                                      _cache[13] ||
                                          (_cache[13] = [
                                              createBaseVNode(
                                                  "svg",
                                                  {
                                                      width: "18",
                                                      height: "17",
                                                      viewBox: "0 0 24 22",
                                                      fill: "none",
                                                      xmlns: "http://www.w3.org/2000/svg",
                                                  },
                                                  [
                                                      createBaseVNode("path", {
                                                          d: "M10.2455 1.20652C11.0042 -0.180108 12.9958 -0.180108 13.7545 1.20652L15.9155 5.15589C16.203 5.68124 16.7107 6.05014 17.2992 6.16118L21.723 6.99598C23.2763 7.28908 23.8917 9.1832 22.8074 10.3333L19.7191 13.6089C19.3083 14.0447 19.1143 14.6415 19.1906 15.2355L19.7637 19.7008C19.9649 21.2686 18.3537 22.4392 16.9248 21.7634L12.8551 19.8385C12.3138 19.5824 11.6862 19.5824 11.1449 19.8385L7.07519 21.7634C5.64633 22.4392 4.0351 21.2686 4.23631 19.7008L4.80942 15.2355C4.88566 14.6415 4.69172 14.0447 4.28091 13.6089L1.19262 10.3333C0.108311 9.1832 0.723747 7.28908 2.27697 6.99598L6.70083 6.16118C7.28929 6.05014 7.79703 5.68124 8.08449 5.15589L10.2455 1.20652Z",
                                                      }),
                                                  ],
                                                  -1,
                                              ),
                                          ]),
                                  ),
                              ]))
                            : createCommentVNode("", true),
                        unref(configStore).rating.type === "thumbs"
                            ? (openBlock(),
                              createElementBlock("div", _hoisted_5, [
                                  createBaseVNode(
                                      "a",
                                      {
                                          class: "thumb-up",
                                          onClick:
                                              _cache[5] ||
                                              (_cache[5] = ($event) => rate(5)),
                                      },
                                      _cache[14] ||
                                          (_cache[14] = [
                                              createBaseVNode(
                                                  "svg",
                                                  {
                                                      viewBox: "0 0 24 24",
                                                      fill: "none",
                                                      xmlns: "http://www.w3.org/2000/svg",
                                                  },
                                                  [
                                                      createBaseVNode("path", {
                                                          d: "M2.25 10.625V18.875C2.25 19.6344 2.86561 20.25 3.625 20.25C4.38439 20.25 5 19.6344 5 18.875V10.625C5 9.86561 4.38439 9.25 3.625 9.25C2.86561 9.25 2.25 9.86561 2.25 10.625Z",
                                                          stroke: "#0D0E16",
                                                          "stroke-linecap":
                                                              "round",
                                                          "stroke-linejoin":
                                                              "round",
                                                      }),
                                                      createBaseVNode("path", {
                                                          d: "M16.2494 8.25681C16.627 6.93685 16.6261 5.80164 16.2466 4.61039C16.0231 3.90895 15.3391 3.5 14.6178 3.5C14.1127 3.5 13.6322 3.71626 13.4283 4.18885C13.0289 5.11465 12.3546 7.01836 11.3717 8.0236C9.91091 9.51762 8.83793 10.386 7 11.0217V18.1244L7.74938 18.8306C8.30255 19.3518 8.57914 19.6125 8.91804 19.7975C9.02624 19.8565 9.15107 19.9147 9.26593 19.9594C9.62567 20.0997 9.98516 20.1415 10.7041 20.225H10.7041C11.7568 20.3474 13.0779 20.4796 14 20.4976C14.6935 20.5112 15.51 20.4655 16.2986 20.3972C17.5835 20.2859 18.226 20.2303 18.8253 19.8665C19.0096 19.7547 19.2141 19.5959 19.3681 19.445C19.8689 18.9544 20.101 18.291 20.565 16.9644L21.7594 13.5502C22.3845 11.7632 21.1405 9.87067 19.2522 9.73572L17.268 9.59391C16.5951 9.54582 16.1171 8.91828 16.2494 8.25681Z",
                                                          stroke: "#0D0E16",
                                                          "stroke-linecap":
                                                              "round",
                                                          "stroke-linejoin":
                                                              "round",
                                                      }),
                                                  ],
                                                  -1,
                                              ),
                                          ]),
                                  ),
                                  createBaseVNode(
                                      "a",
                                      {
                                          class: "thumb-down",
                                          onClick:
                                              _cache[6] ||
                                              (_cache[6] = ($event) => rate(1)),
                                      },
                                      _cache[15] ||
                                          (_cache[15] = [
                                              createBaseVNode(
                                                  "svg",
                                                  {
                                                      viewBox: "0 0 24 24",
                                                      fill: "none",
                                                      xmlns: "http://www.w3.org/2000/svg",
                                                  },
                                                  [
                                                      createBaseVNode("path", {
                                                          d: "M2.25 13.375V5.125C2.25 4.36561 2.86561 3.75 3.625 3.75C4.38439 3.75 5 4.36561 5 5.125V13.375C5 14.1344 4.38439 14.75 3.625 14.75C2.86561 14.75 2.25 14.1344 2.25 13.375Z",
                                                          stroke: "#0D0E16",
                                                          "stroke-linecap":
                                                              "round",
                                                          "stroke-linejoin":
                                                              "round",
                                                      }),
                                                      createBaseVNode("path", {
                                                          d: "M16.2494 15.7432C16.627 17.0631 16.6261 18.1984 16.2466 19.3896C16.0231 20.0911 15.3391 20.5 14.6178 20.5C14.1127 20.5 13.6322 20.2837 13.4283 19.8111C13.0289 18.8853 12.3546 16.9816 11.3717 15.9764C9.91091 14.4824 8.83793 13.614 7 12.9783V5.87559L7.74938 5.16943C8.30255 4.64817 8.57914 4.38754 8.91804 4.20252C9.02624 4.14345 9.15107 4.08533 9.26593 4.04056C9.62567 3.90032 9.98516 3.85854 10.7041 3.77498H10.7041C11.7568 3.65264 13.0779 3.52041 14 3.50238C14.6935 3.48882 15.51 3.53455 16.2986 3.60281C17.5835 3.71405 18.226 3.76967 18.8253 4.13346C19.0096 4.24533 19.2141 4.40413 19.3681 4.555C19.8689 5.04565 20.101 5.70897 20.565 7.0356L21.7594 10.4498C22.3845 12.2368 21.1405 14.1293 19.2522 14.2643L17.268 14.4061C16.5951 14.4542 16.1171 15.0817 16.2494 15.7432Z",
                                                          stroke: "#0D0E16",
                                                          "stroke-linecap":
                                                              "round",
                                                          "stroke-linejoin":
                                                              "round",
                                                      }),
                                                  ],
                                                  -1,
                                              ),
                                          ]),
                                  ),
                              ]))
                            : createCommentVNode("", true),
                        unref(configStore).rating.type === "review"
                            ? (openBlock(),
                              createElementBlock("div", _hoisted_6, [
                                  createBaseVNode(
                                      "a",
                                      {
                                          class: "ysd-write-review-accept",
                                          onClick:
                                              _cache[7] ||
                                              (_cache[7] = ($event) => rate(5)),
                                      },
                                      toDisplayString(
                                          unref(configStore).rating.accept,
                                      ),
                                      1,
                                  ),
                                  createBaseVNode(
                                      "div",
                                      {
                                          class: "ysd-write-review-reject",
                                          onClick:
                                              _cache[8] ||
                                              (_cache[8] = ($event) => rate(1)),
                                      },
                                      [
                                          createBaseVNode(
                                              "span",
                                              null,
                                              toDisplayString(
                                                  unref(configStore).rating
                                                      .reject,
                                              ),
                                              1,
                                          ),
                                      ],
                                  ),
                              ]))
                            : createCommentVNode("", true),
                    ]),
                ])
            );
        };
    },
};
const YsdRating = /* @__PURE__ */ _export_sfc(_sfc_main$1, [
    ["__scopeId", "data-v-c3f5eabb"],
]);
const _hoisted_1 = { class: "app" };
const _hoisted_2 = { class: "header" };
const _hoisted_3 = { class: "main" };
const _hoisted_4 = {
    key: 0,
    class: "app-loader",
};
const _sfc_main = {
    __name: "AppSidePanel",
    setup(__props) {
        const userStore = useUserStore();
        const configStore = useConfigStore();
        const panelStore = usePanelStore();
        const videoInfoStore = useVideoInfoStore();
        const subtitlesStore = useSubtitlesStore();
        const isAppLoading = ref(true);
        onMounted(async () => {
            try {
                await panelStore.initTheme();
                await userStore.init();
                await configStore.init();
                await videoInfoStore.init();
                videoInfoStore.isInitialized = true;
                isAppLoading.value = false;
            } catch (e) {}
        });
        return (_ctx, _cache) => {
            return (
                openBlock(),
                createElementBlock("div", _hoisted_1, [
                    createBaseVNode("div", _hoisted_2, [
                        createVNode(YsdPanel),
                        createBaseVNode(
                            "div",
                            {
                                class: normalizeClass([
                                    "loader",
                                    {
                                        "loader-active":
                                            unref(subtitlesStore).isDownloading,
                                    },
                                ]),
                            },
                            null,
                            2,
                        ),
                    ]),
                    createBaseVNode("div", _hoisted_3, [
                        createVNode(YsdSubtitles),
                    ]),
                    _cache[1] ||
                        (_cache[1] = createBaseVNode(
                            "div",
                            { class: "noise" },
                            null,
                            -1,
                        )),
                    isAppLoading.value
                        ? (openBlock(),
                          createElementBlock(
                              "div",
                              _hoisted_4,
                              _cache[0] ||
                                  (_cache[0] = [
                                      createBaseVNode(
                                          "div",
                                          { class: "text-shine" },
                                          "Initializing...",
                                          -1,
                                      ),
                                  ]),
                          ))
                        : createCommentVNode("", true),
                    unref(configStore).rating.show
                        ? (openBlock(), createBlock(YsdRating, { key: 1 }))
                        : createCommentVNode("", true),
                ])
            );
        };
    },
};
const mountNode = document.getElementById("app");
const pinia = createPinia();
pinia.use(src_default);
createApp(_sfc_main).use(pinia).mount(mountNode);
