[{"description": "treehash per file", "signed_content": {"payload": "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", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "vecUNTyokwKx7ZsILigfif2B5VYPLlsj5za4oSN4ewT7xPijUpkxGR-jF9EFtKg91ch7m9FhhRNbwa-0q8Cw5a7XMpY-xTnOctX6uvNncJRVoufc8rOsDWEXuM93yVYIKM2JQbxmQikBjbdbR1IAO5VFH7r7BrdrhqOGA5_K51O3sRE9q_hcJvZU5N2AC6QQsjFdnmpxNmF94yo5lVRyDQHSbmH_MPqgRTTQKoIaXhgKQcwiF1f8MQbJPKJT3yO7_KB2mE2TUs6n8W6TGToPp5qkpI7YFeD2zBfwj_DFS_mbMjw0Dhi5FAuKvWztLcNFshr0QhMs5inNoue6Ah71fA"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "jrhv-zF6E3uXnMZcYySHC8mWTgDIRNPJ6n7mxBm3PWfN5SzgJP6NlKULm8vpC_jZjag6pmhFNv9TZTEtXcozLjxW_XohW-lZH3EMMUrxq32hUDQrAUlzxgfbDaWjnbhauEAq1NXXWNSnP8x6KW4-vO1ZUUx5T10UCTxVQnO01pHvESAACDg4CFh-zuBGPYq3uugjgEwZig2DtyVJKEWCXeN1T12WKg-aHGDH38bi-UatzUGadO971v5qkOptjpkd9-3Z6-Az_w2hcH5zHiB3eohNntzLR9CrrD2jzA5TAmxKCOZlCF3V1xxcEjKBVA_b8ybhvAaDGv675L2DxYcgFw"}]}}]